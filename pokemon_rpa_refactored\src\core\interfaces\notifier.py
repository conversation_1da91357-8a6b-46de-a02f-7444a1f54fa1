"""Notifier interface definition."""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional


class INotifier(ABC):
    """Abstract interface for sending notifications."""
    
    @abstractmethod
    async def send_message(
        self,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send a general message notification.
        
        Args:
            message: Message content to send
            metadata: Optional metadata to include with message
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def send_success_notification(
        self,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send a success notification.
        
        Args:
            message: Success message content
            metadata: Optional metadata to include
            
        Returns:
            True if notification was sent successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def send_error_notification(
        self,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send an error notification.
        
        Args:
            message: Error message content
            metadata: Optional metadata to include
            
        Returns:
            True if notification was sent successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def send_warning_notification(
        self,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send a warning notification.
        
        Args:
            message: Warning message content
            metadata: Optional metadata to include
            
        Returns:
            True if notification was sent successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def send_info_notification(
        self,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send an info notification.
        
        Args:
            message: Info message content
            metadata: Optional metadata to include
            
        Returns:
            True if notification was sent successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> dict:
        """
        Perform health check of notifier.
        
        Returns:
            Dictionary containing health status and diagnostics
        """
        pass
