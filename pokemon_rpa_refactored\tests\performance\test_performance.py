"""Performance tests for the Pokemon RPA Framework."""

import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import AsyncMock, MagicMock
import numpy as np

from src.infrastructure.config.container import configure_container
from src.core.entities.device import Device, DeviceStatus, DeviceType


@pytest.mark.performance
@pytest.mark.asyncio
class TestPerformance:
    """Performance tests for critical system components."""
    
    @pytest.fixture
    async def container(self):
        """Create configured container for performance testing."""
        container = configure_container()
        yield container
        await container.shutdown_resources()
    
    @pytest.fixture
    def mock_device(self):
        """Create mock device for testing."""
        return Device(
            device_id="perf_test_device",
            status=DeviceStatus.CONNECTED,
            device_type=DeviceType.ANDROID,
            screen_resolution=(1080, 1920)
        )
    
    async def test_automation_service_throughput(self, container, mock_device):
        """Test automation service throughput under load."""
        automation_service = container.automation_service()
        
        # Mock dependencies for performance testing
        device_controller = container.adb_device_controller()
        device_controller.connect_device = AsyncMock(return_value=True)
        device_controller.is_device_connected = AsyncMock(return_value=True)
        device_controller.take_screenshot = AsyncMock(return_value=b"fake_screenshot")
        
        # Performance test parameters
        num_concurrent_sessions = 10
        test_duration = 30  # seconds
        
        async def start_automation_session(session_num):
            """Start a single automation session."""
            start_time = time.time()
            try:
                session_id = await automation_service.start_adventure_automation(
                    device_id=f"{mock_device.device_id}_{session_num}",
                    user_name=f"perf_user_{session_num}"
                )
                
                # Simulate some work
                await asyncio.sleep(0.1)
                
                # Stop the session
                await automation_service.stop_automation(session_id)
                
                end_time = time.time()
                return {
                    "session_id": session_id,
                    "duration": end_time - start_time,
                    "success": True
                }
            except Exception as e:
                end_time = time.time()
                return {
                    "session_id": None,
                    "duration": end_time - start_time,
                    "success": False,
                    "error": str(e)
                }
        
        # Run concurrent sessions
        start_time = time.time()
        tasks = [
            start_automation_session(i) 
            for i in range(num_concurrent_sessions)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Analyze results
        successful_sessions = sum(1 for r in results if isinstance(r, dict) and r.get("success"))
        total_duration = end_time - start_time
        throughput = successful_sessions / total_duration
        
        # Performance assertions
        assert successful_sessions >= num_concurrent_sessions * 0.8  # 80% success rate
        assert throughput >= 1.0  # At least 1 session per second
        assert total_duration < test_duration  # Complete within time limit
        
        print(f"Performance Results:")
        print(f"  Successful sessions: {successful_sessions}/{num_concurrent_sessions}")
        print(f"  Total duration: {total_duration:.2f}s")
        print(f"  Throughput: {throughput:.2f} sessions/second")
    
    async def test_image_detection_performance(self, container):
        """Test computer vision performance."""
        image_detector = container.opencv_image_detector()
        
        # Create test image
        test_image = np.random.randint(0, 255, (1920, 1080, 3), dtype=np.uint8)
        template_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # Mock template loading
        image_detector._load_template = MagicMock(return_value=template_image)
        
        # Performance test parameters
        num_detections = 100
        max_detection_time = 0.1  # 100ms per detection
        
        detection_times = []
        
        for i in range(num_detections):
            start_time = time.time()
            
            try:
                result = await image_detector.detect_template(test_image, "test_template")
                end_time = time.time()
                detection_time = end_time - start_time
                detection_times.append(detection_time)
                
                # Ensure detection completes within time limit
                assert detection_time < max_detection_time
                
            except Exception as e:
                pytest.fail(f"Detection failed on iteration {i}: {e}")
        
        # Analyze performance
        avg_detection_time = sum(detection_times) / len(detection_times)
        max_detection_time_actual = max(detection_times)
        min_detection_time = min(detection_times)
        
        # Performance assertions
        assert avg_detection_time < 0.05  # Average under 50ms
        assert max_detection_time_actual < 0.1  # Max under 100ms
        
        print(f"Image Detection Performance:")
        print(f"  Average time: {avg_detection_time*1000:.2f}ms")
        print(f"  Max time: {max_detection_time_actual*1000:.2f}ms")
        print(f"  Min time: {min_detection_time*1000:.2f}ms")
    
    async def test_ocr_performance(self, container):
        """Test OCR performance."""
        ocr_reader = container.tesseract_ocr_reader()
        
        # Create test image with text
        test_image = np.ones((200, 400, 3), dtype=np.uint8) * 255
        
        # Mock OCR operations
        ocr_reader._extract_text_internal = AsyncMock(return_value=["Test", "Text"])
        
        # Performance test parameters
        num_ocr_operations = 50
        max_ocr_time = 0.5  # 500ms per OCR operation
        
        ocr_times = []
        
        for i in range(num_ocr_operations):
            start_time = time.time()
            
            try:
                result = await ocr_reader.extract_text(test_image)
                end_time = time.time()
                ocr_time = end_time - start_time
                ocr_times.append(ocr_time)
                
                # Ensure OCR completes within time limit
                assert ocr_time < max_ocr_time
                
            except Exception as e:
                pytest.fail(f"OCR failed on iteration {i}: {e}")
        
        # Analyze performance
        avg_ocr_time = sum(ocr_times) / len(ocr_times)
        max_ocr_time_actual = max(ocr_times)
        
        # Performance assertions
        assert avg_ocr_time < 0.2  # Average under 200ms
        
        print(f"OCR Performance:")
        print(f"  Average time: {avg_ocr_time*1000:.2f}ms")
        print(f"  Max time: {max_ocr_time_actual*1000:.2f}ms")
    
    async def test_database_performance(self, container):
        """Test database operation performance."""
        repository = container.database_repository()
        
        # Mock database operations
        repository.save_device = AsyncMock(return_value=True)
        repository.get_device = AsyncMock(return_value=None)
        repository.list_devices = AsyncMock(return_value=[])
        repository.save_automation_session = AsyncMock(return_value=True)
        repository.get_automation_session = AsyncMock(return_value=None)
        
        # Performance test parameters
        num_operations = 200
        max_operation_time = 0.1  # 100ms per operation
        
        operation_times = []
        
        # Test various database operations
        operations = [
            ("save_device", lambda: repository.save_device(mock_device)),
            ("get_device", lambda: repository.get_device("test_id")),
            ("list_devices", lambda: repository.list_devices()),
        ]
        
        for i in range(num_operations):
            operation_name, operation_func = operations[i % len(operations)]
            
            start_time = time.time()
            try:
                await operation_func()
                end_time = time.time()
                operation_time = end_time - start_time
                operation_times.append((operation_name, operation_time))
                
                # Ensure operation completes within time limit
                assert operation_time < max_operation_time
                
            except Exception as e:
                pytest.fail(f"Database operation {operation_name} failed: {e}")
        
        # Analyze performance by operation type
        operation_stats = {}
        for op_name, op_time in operation_times:
            if op_name not in operation_stats:
                operation_stats[op_name] = []
            operation_stats[op_name].append(op_time)
        
        for op_name, times in operation_stats.items():
            avg_time = sum(times) / len(times)
            max_time = max(times)
            
            print(f"Database {op_name} Performance:")
            print(f"  Average time: {avg_time*1000:.2f}ms")
            print(f"  Max time: {max_time*1000:.2f}ms")
            
            # Performance assertions
            assert avg_time < 0.05  # Average under 50ms
    
    async def test_memory_usage(self, container, mock_device):
        """Test memory usage under load."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        automation_service = container.automation_service()
        
        # Mock dependencies
        device_controller = container.adb_device_controller()
        device_controller.connect_device = AsyncMock(return_value=True)
        device_controller.is_device_connected = AsyncMock(return_value=True)
        
        # Create multiple sessions to test memory usage
        session_ids = []
        num_sessions = 20
        
        for i in range(num_sessions):
            session_id = await automation_service.start_adventure_automation(
                device_id=f"{mock_device.device_id}_{i}",
                user_name=f"memory_test_user_{i}"
            )
            session_ids.append(session_id)
            
            # Check memory after each session
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = current_memory - initial_memory
            
            # Memory should not increase excessively
            assert memory_increase < 100  # Less than 100MB increase
        
        # Clean up sessions
        for session_id in session_ids:
            await automation_service.stop_automation(session_id)
        
        # Check final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_memory_increase = final_memory - initial_memory
        
        print(f"Memory Usage Test:")
        print(f"  Initial memory: {initial_memory:.2f}MB")
        print(f"  Final memory: {final_memory:.2f}MB")
        print(f"  Total increase: {total_memory_increase:.2f}MB")
        
        # Final memory assertion
        assert total_memory_increase < 50  # Less than 50MB total increase
    
    async def test_concurrent_device_operations(self, container):
        """Test concurrent device operations performance."""
        device_service = container.device_service()
        
        # Mock device controller
        device_controller = container.adb_device_controller()
        device_controller.list_devices = AsyncMock(return_value=["device1", "device2", "device3"])
        device_controller.is_device_connected = AsyncMock(return_value=True)
        device_controller.get_device_info = AsyncMock(return_value=None)
        
        # Performance test parameters
        num_concurrent_operations = 50
        max_total_time = 5.0  # 5 seconds for all operations
        
        async def perform_device_operation(operation_id):
            """Perform a device operation."""
            start_time = time.time()
            try:
                # Mix of different operations
                if operation_id % 3 == 0:
                    await device_service.list_connected_devices()
                elif operation_id % 3 == 1:
                    await device_service.check_device_health(f"device_{operation_id}")
                else:
                    await device_service.get_device_info(f"device_{operation_id}")
                
                end_time = time.time()
                return {
                    "operation_id": operation_id,
                    "duration": end_time - start_time,
                    "success": True
                }
            except Exception as e:
                end_time = time.time()
                return {
                    "operation_id": operation_id,
                    "duration": end_time - start_time,
                    "success": False,
                    "error": str(e)
                }
        
        # Run concurrent operations
        start_time = time.time()
        tasks = [
            perform_device_operation(i) 
            for i in range(num_concurrent_operations)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        total_time = end_time - start_time
        successful_operations = sum(1 for r in results if isinstance(r, dict) and r.get("success"))
        
        # Performance assertions
        assert total_time < max_total_time
        assert successful_operations >= num_concurrent_operations * 0.9  # 90% success rate
        
        print(f"Concurrent Device Operations Performance:")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Successful operations: {successful_operations}/{num_concurrent_operations}")
        print(f"  Operations per second: {successful_operations/total_time:.2f}")


@pytest.mark.performance
def test_startup_time():
    """Test application startup time."""
    start_time = time.time()
    
    # Import and configure container (simulating app startup)
    from src.infrastructure.config.container import configure_container
    container = configure_container()
    
    end_time = time.time()
    startup_time = end_time - start_time
    
    # Startup should be fast
    assert startup_time < 2.0  # Less than 2 seconds
    
    print(f"Startup Performance:")
    print(f"  Startup time: {startup_time:.3f}s")
