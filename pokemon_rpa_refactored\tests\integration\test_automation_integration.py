"""Integration tests for automation workflow."""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime

from src.core.entities.device import Device, DeviceStatus, DeviceType
from src.core.entities.automation import AutomationSession, AutomationStatus
from src.infrastructure.config.container import configure_container


@pytest.mark.integration
@pytest.mark.asyncio
class TestAutomationIntegration:
    """Integration tests for the complete automation workflow."""
    
    @pytest.fixture
    async def container(self):
        """Create and configure dependency injection container."""
        container = configure_container()
        yield container
        await container.shutdown_resources()
    
    @pytest.fixture
    def mock_device(self):
        """Create a mock device for testing."""
        return Device(
            device_id="test_device_123",
            status=DeviceStatus.CONNECTED,
            device_type=DeviceType.ANDROID,
            screen_resolution=(1080, 1920)
        )
    
    async def test_complete_automation_workflow(self, container, mock_device):
        """Test the complete automation workflow from start to finish."""
        # Get services from container
        automation_service = container.automation_service()
        device_service = container.device_service()
        
        # Mock device controller to avoid real device interactions
        device_controller = container.adb_device_controller()
        device_controller.connect_device = AsyncMock(return_value=True)
        device_controller.get_device_info = AsyncMock(return_value=mock_device.info)
        device_controller.take_screenshot = AsyncMock(return_value=b"fake_screenshot")
        device_controller.tap = AsyncMock(return_value=True)
        device_controller.swipe = AsyncMock(return_value=True)
        device_controller.is_device_connected = AsyncMock(return_value=True)
        
        # Mock image detector
        image_detector = container.opencv_image_detector()
        image_detector.detect_template = AsyncMock(return_value=(100, 100, 0.9))
        image_detector.detect_multiple_templates = AsyncMock(return_value=[
            {"template": "adventure_button", "location": (100, 100), "confidence": 0.9}
        ])
        
        # Mock OCR reader
        ocr_reader = container.tesseract_ocr_reader()
        ocr_reader.extract_text = AsyncMock(return_value=["Adventure", "Start"])
        ocr_reader.extract_text_with_confidence = AsyncMock(return_value=[
            {"text": "Adventure", "confidence": 95.0, "coordinates": (50, 50, 150, 80)}
        ])
        
        # Mock notifier
        notifier = container.telegram_notifier()
        notifier.send_message = AsyncMock(return_value=True)
        notifier.send_success_notification = AsyncMock(return_value=True)
        
        # Test device connection
        connected_devices = await device_service.list_connected_devices()
        assert isinstance(connected_devices, list)
        
        # Test device registration
        device_info = await device_service.get_device_info(mock_device.device_id)
        if device_info is None:
            # Register device if not exists
            await device_service.register_device(mock_device)
        
        # Test automation start
        session_id = await automation_service.start_adventure_automation(
            device_id=mock_device.device_id,
            user_name="test_user"
        )
        
        assert session_id is not None
        assert isinstance(session_id, str)
        
        # Wait a bit for automation to process
        await asyncio.sleep(0.1)
        
        # Test session status
        session_status = await automation_service.get_session_status(session_id)
        assert session_status is not None
        assert session_status["session_id"] == session_id
        
        # Test stopping automation
        stopped = await automation_service.stop_automation(session_id)
        assert stopped is True
        
        # Verify final session state
        final_session = await automation_service.get_session_status(session_id)
        assert final_session["status"] in [AutomationStatus.COMPLETED.value, AutomationStatus.STOPPED.value]
    
    async def test_device_health_checks(self, container, mock_device):
        """Test device health check functionality."""
        device_service = container.device_service()
        
        # Mock device controller
        device_controller = container.adb_device_controller()
        device_controller.is_device_connected = AsyncMock(return_value=True)
        device_controller.get_device_info = AsyncMock(return_value=mock_device.info)
        
        # Test health check
        health_status = await device_service.check_device_health(mock_device.device_id)
        
        assert health_status is not None
        assert "healthy" in health_status
        assert "device_id" in health_status
    
    async def test_automation_error_handling(self, container, mock_device):
        """Test automation error handling and recovery."""
        automation_service = container.automation_service()
        
        # Mock device controller to simulate errors
        device_controller = container.adb_device_controller()
        device_controller.connect_device = AsyncMock(side_effect=Exception("Device connection failed"))
        device_controller.is_device_connected = AsyncMock(return_value=False)
        
        # Mock notifier
        notifier = container.telegram_notifier()
        notifier.send_error_notification = AsyncMock(return_value=True)
        
        # Test automation with device error
        session_id = await automation_service.start_adventure_automation(
            device_id=mock_device.device_id,
            user_name="test_user"
        )
        
        # Wait for error processing
        await asyncio.sleep(0.1)
        
        # Check session status shows error
        session_status = await automation_service.get_session_status(session_id)
        assert session_status["status"] == AutomationStatus.FAILED.value
        assert session_status["error_message"] is not None
        
        # Verify error notification was sent
        notifier.send_error_notification.assert_called()
    
    async def test_session_persistence(self, container, mock_device):
        """Test session persistence and retrieval."""
        automation_service = container.automation_service()
        
        # Mock dependencies
        device_controller = container.adb_device_controller()
        device_controller.connect_device = AsyncMock(return_value=True)
        device_controller.is_device_connected = AsyncMock(return_value=True)
        
        # Start automation
        session_id = await automation_service.start_adventure_automation(
            device_id=mock_device.device_id,
            user_name="test_user"
        )
        
        # Test session listing
        sessions = await automation_service.list_user_sessions("test_user")
        assert len(sessions) > 0
        assert any(session["session_id"] == session_id for session in sessions)
        
        # Test session retrieval
        session_details = await automation_service.get_session_status(session_id)
        assert session_details["session_id"] == session_id
        assert session_details["user_name"] == "test_user"
        assert session_details["device_id"] == mock_device.device_id
    
    async def test_automation_statistics(self, container, mock_device):
        """Test automation statistics collection."""
        automation_service = container.automation_service()
        
        # Mock dependencies
        device_controller = container.adb_device_controller()
        device_controller.connect_device = AsyncMock(return_value=True)
        device_controller.is_device_connected = AsyncMock(return_value=True)
        
        # Start and complete automation
        session_id = await automation_service.start_adventure_automation(
            device_id=mock_device.device_id,
            user_name="test_user"
        )
        
        await asyncio.sleep(0.1)
        await automation_service.stop_automation(session_id)
        
        # Test statistics
        stats = await automation_service.get_automation_statistics()
        
        assert "total_sessions" in stats
        assert "successful_sessions" in stats
        assert "failed_sessions" in stats
        assert "average_duration" in stats
        assert stats["total_sessions"] >= 1
    
    async def test_cleanup_operations(self, container):
        """Test cleanup operations."""
        automation_service = container.automation_service()
        
        # Test cleanup old sessions
        cleaned_count = await automation_service.cleanup_old_sessions(days=0)  # Clean all
        assert isinstance(cleaned_count, int)
        assert cleaned_count >= 0


@pytest.mark.integration
@pytest.mark.asyncio
class TestDatabaseIntegration:
    """Integration tests for database operations."""
    
    @pytest.fixture
    async def container(self):
        """Create container with real database connection."""
        container = configure_container()
        yield container
        await container.shutdown_resources()
    
    async def test_device_crud_operations(self, container):
        """Test device CRUD operations with real database."""
        repository = container.database_repository()
        
        # Create test device
        test_device = Device(
            device_id="integration_test_device",
            status=DeviceStatus.CONNECTED,
            device_type=DeviceType.ANDROID,
            screen_resolution=(1080, 1920)
        )
        
        # Test save
        saved = await repository.save_device(test_device)
        assert saved is True
        
        # Test retrieve
        retrieved_device = await repository.get_device(test_device.device_id)
        assert retrieved_device is not None
        assert retrieved_device.device_id == test_device.device_id
        assert retrieved_device.status == test_device.status
        
        # Test list
        devices = await repository.list_devices()
        assert any(d.device_id == test_device.device_id for d in devices)
        
        # Test delete
        deleted = await repository.delete_device(test_device.device_id)
        assert deleted is True
        
        # Verify deletion
        deleted_device = await repository.get_device(test_device.device_id)
        assert deleted_device is None
    
    async def test_session_crud_operations(self, container):
        """Test automation session CRUD operations."""
        repository = container.database_repository()
        
        # Create test session
        test_session = AutomationSession(
            session_id="integration_test_session",
            user_name="test_user",
            device_id="test_device",
            session_type="adventure",
            status=AutomationStatus.RUNNING,
            started_at=datetime.utcnow()
        )
        
        # Test save
        saved = await repository.save_automation_session(test_session)
        assert saved is True
        
        # Test retrieve
        retrieved_session = await repository.get_automation_session(test_session.session_id)
        assert retrieved_session is not None
        assert retrieved_session.session_id == test_session.session_id
        assert retrieved_session.user_name == test_session.user_name
        
        # Test update
        test_session.status = AutomationStatus.COMPLETED
        updated = await repository.update_automation_session(test_session)
        assert updated is True
        
        # Test list operations
        sessions = await repository.list_automation_sessions()
        assert any(s.session_id == test_session.session_id for s in sessions)
        
        user_sessions = await repository.get_user_sessions("test_user")
        assert any(s.session_id == test_session.session_id for s in user_sessions)
        
        # Test cleanup
        await repository.delete_automation_session(test_session.session_id)
    
    async def test_database_health_check(self, container):
        """Test database health check."""
        repository = container.database_repository()
        
        health_status = await repository.health_check()
        
        assert "healthy" in health_status
        assert health_status["healthy"] is True
        assert "connection_ok" in health_status
