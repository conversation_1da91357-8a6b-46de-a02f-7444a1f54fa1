"""Unit tests for adventure automation use case."""

import pytest
import asyncio
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
import numpy as np

from src.core.use_cases.adventure_automation import AdventureAutomationUseCase
from src.core.entities.automation import (
    AutomationSession, 
    AutomationStatus, 
    DetectionResult,
    Coordinates,
    GameState
)
from src.core.entities.device import Device, DeviceStatus


class TestAdventureAutomationUseCase:
    """Test suite for adventure automation use case."""
    
    @pytest.fixture
    def mock_device_controller(self):
        """Mock device controller."""
        controller = AsyncMock()
        controller.is_device_connected.return_value = True
        controller.take_screenshot.return_value = np.zeros((1080, 1920, 3), dtype=np.uint8)
        return controller
    
    @pytest.fixture
    def mock_image_detector(self):
        """Mock image detector."""
        detector = AsyncMock()
        detector.detect_multiple_templates.return_value = [
            DetectionResult(
                found=True,
                confidence=0.9,
                coordinates=Coordinates(500, 300),
                template_name="adventure.png"
            )
        ]
        return detector
    
    @pytest.fixture
    def mock_ocr_reader(self):
        """Mock OCR reader."""
        reader = AsyncMock()
        reader.extract_text.return_value = ["Adventure", "Battle", "Continue"]
        return reader
    
    @pytest.fixture
    def use_case(self, mock_device_controller, mock_image_detector, mock_ocr_reader):
        """Create use case instance with mocked dependencies."""
        return AdventureAutomationUseCase(
            device_controller=mock_device_controller,
            image_detector=mock_image_detector,
            ocr_reader=mock_ocr_reader
        )
    
    @pytest.mark.asyncio
    async def test_execute_adventure_success(self, use_case, mock_device_controller):
        """Test successful adventure execution."""
        # Arrange
        device_id = "test_device"
        user_name = "test_user"
        
        # Act
        session = await use_case.execute_adventure(
            device_id=device_id,
            user_name=user_name,
            max_duration_seconds=5  # Short duration for testing
        )
        
        # Assert
        assert session.device_id == device_id
        assert session.user_name == user_name
        assert session.session_type == "adventure_automation"
        assert session.status == AutomationStatus.COMPLETED
        assert session.started_at is not None
        assert session.completed_at is not None
        assert len(session.actions) > 0
        
        # Verify device controller was called
        mock_device_controller.is_device_connected.assert_called_once_with(device_id)
        mock_device_controller.take_screenshot.assert_called()
    
    @pytest.mark.asyncio
    async def test_execute_adventure_device_not_connected(self, use_case, mock_device_controller):
        """Test adventure execution with disconnected device."""
        # Arrange
        device_id = "disconnected_device"
        user_name = "test_user"
        mock_device_controller.is_device_connected.return_value = False
        
        # Act
        session = await use_case.execute_adventure(
            device_id=device_id,
            user_name=user_name,
            max_duration_seconds=1
        )
        
        # Assert
        assert session.status == AutomationStatus.FAILED
        assert "not connected" in session.error_message
    
    @pytest.mark.asyncio
    async def test_analyze_game_state(self, use_case, mock_device_controller, mock_image_detector, mock_ocr_reader):
        """Test game state analysis."""
        # Arrange
        device_id = "test_device"
        
        # Act
        game_state = await use_case._analyze_game_state(device_id)
        
        # Assert
        assert isinstance(game_state, GameState)
        assert game_state.screen_name == "adventure_selection"
        assert len(game_state.detected_elements) > 0
        assert len(game_state.ocr_text) > 0
        
        # Verify calls
        mock_device_controller.take_screenshot.assert_called_once_with(device_id)
        mock_image_detector.detect_multiple_templates.assert_called_once()
        mock_ocr_reader.extract_text.assert_called_once()
    
    def test_identify_screen_adventure_selection(self, use_case):
        """Test screen identification for adventure selection."""
        # Arrange
        detection_results = [
            DetectionResult(
                found=True,
                confidence=0.9,
                coordinates=Coordinates(500, 300),
                template_name="adventure.png"
            )
        ]
        
        # Act
        screen_name = use_case._identify_screen(detection_results)
        
        # Assert
        assert screen_name == "adventure_selection"
    
    def test_identify_screen_battle(self, use_case):
        """Test screen identification for battle."""
        # Arrange
        detection_results = [
            DetectionResult(
                found=True,
                confidence=0.9,
                coordinates=Coordinates(500, 300),
                template_name="battle.png"
            )
        ]
        
        # Act
        screen_name = use_case._identify_screen(detection_results)
        
        # Assert
        assert screen_name == "battle"
    
    def test_identify_screen_unknown(self, use_case):
        """Test screen identification for unknown screen."""
        # Arrange
        detection_results = [
            DetectionResult(
                found=False,
                confidence=0.3,
                template_name="unknown.png"
            )
        ]
        
        # Act
        screen_name = use_case._identify_screen(detection_results)
        
        # Assert
        assert screen_name == "unknown"
    
    @pytest.mark.asyncio
    async def test_determine_next_action_adventure_selection(self, use_case):
        """Test action determination for adventure selection screen."""
        # Arrange
        game_state = GameState(
            screen_name="adventure_selection",
            detected_elements=[
                DetectionResult(
                    found=True,
                    confidence=0.9,
                    coordinates=Coordinates(500, 300),
                    template_name="adventure.png"
                )
            ]
        )
        
        # Act
        action = await use_case._determine_next_action(game_state)
        
        # Assert
        assert action is not None
        assert action.action_type.value == "click"
        assert action.parameters["coordinates"] == Coordinates(500, 300)
    
    @pytest.mark.asyncio
    async def test_execute_click_action(self, use_case, mock_device_controller):
        """Test execution of click action."""
        # Arrange
        device_id = "test_device"
        action = Mock()
        action.action_type.value = "click"
        action.parameters = {"coordinates": Coordinates(500, 300)}
        action.duration_ms = None
        action.error = None
        
        # Act
        await use_case._execute_action(device_id, action)
        
        # Assert
        mock_device_controller.click.assert_called_once_with(
            device_id, Coordinates(500, 300)
        )
        assert action.duration_ms is not None
        assert action.duration_ms > 0
    
    @pytest.mark.asyncio
    async def test_execute_action_with_error(self, use_case, mock_device_controller):
        """Test action execution with error."""
        # Arrange
        device_id = "test_device"
        action = Mock()
        action.action_type.value = "click"
        action.parameters = {"coordinates": Coordinates(500, 300)}
        action.error = None
        
        mock_device_controller.click.side_effect = Exception("Click failed")
        
        # Act & Assert
        with pytest.raises(Exception, match="Click failed"):
            await use_case._execute_action(device_id, action)
        
        assert action.error == "Click failed"


@pytest.mark.integration
class TestAdventureAutomationIntegration:
    """Integration tests for adventure automation."""
    
    @pytest.mark.asyncio
    async def test_full_automation_flow(self):
        """Test complete automation flow with real-like conditions."""
        # This would be an integration test with actual device
        # For now, we'll skip it in unit tests
        pytest.skip("Integration test - requires actual device")
    
    @pytest.mark.asyncio
    async def test_error_recovery(self):
        """Test error recovery mechanisms."""
        # This would test how the system handles various error conditions
        pytest.skip("Integration test - requires error simulation")


# Test fixtures and utilities
@pytest.fixture
def sample_screenshot():
    """Generate a sample screenshot for testing."""
    return np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8)


@pytest.fixture
def sample_detection_results():
    """Generate sample detection results."""
    return [
        DetectionResult(
            found=True,
            confidence=0.9,
            coordinates=Coordinates(500, 300),
            template_name="adventure.png"
        ),
        DetectionResult(
            found=False,
            confidence=0.3,
            template_name="battle.png"
        )
    ]


# Parametrized tests
@pytest.mark.parametrize("screen_name,expected_action", [
    ("adventure_selection", "click"),
    ("battle", "click"),
    ("adventure_complete", "click"),
    ("unknown", "screenshot")
])
def test_action_determination_parametrized(screen_name, expected_action):
    """Test action determination for different screen types."""
    # This would test the action determination logic
    # for various screen states
    pass
