# -*- coding: utf-8 -*-
"""
Versión funcional que replica exactamente logininfinito.py
Mantiene toda la funcionalidad original pero con mejor organización
"""
import sys
sys.path.append("C:/proyecto")
import time
import datetime
import os
import logging
from subprocess import Pope<PERSON>
from typing import Tuple, Optional
from adb_handler import ADBHandler
from user_manager import UserManager
from location_handler import LocationHandler
from utils import print_current_time
from config import TEMP_DIR
from user2 import DataExtractor
import requests
from KThread import MobileClicker
from shell_input_escaper import ShellInputEscaper
from teleporter import Teleporter

# Logging setup
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# Constants
BOT_TOKEN = '5272662594:AAENS6ImW-0Twhbxr3JmfNl9OIpwqthtJjs'
CHAT_ID = '-1001782327359'

def telegram_bot_sendtext(bot_message: str) -> dict:
    """
    Sends a message to a Telegram chat using a bot.
    
    Args:
        bot_message (str): The message to send to the Telegram chat.
    
    Returns:
        dict: The response from the Telegram API as a JSON object.
    """
    send_text = f'https://api.telegram.org/bot{BOT_TOKEN}/sendMessage'
    payload = {
        'chat_id': CHAT_ID,
        'text': bot_message,
        'parse_mode': 'Markdown'
    }
    try:
        response = requests.get(send_text, params=payload)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logging.error(f"Error sending message to Telegram: {e}")
        return {"ok": False, "error": str(e)}

def send_image(image_file: str, adb: ADBHandler):
    """Sends an image file to a Telegram channel."""
    command = f'curl -s -X POST https://api.telegram.org/bot{BOT_TOKEN}/sendPhoto -F chat_id={CHAT_ID} -F photo=@{image_file}'
    process = Popen(command.split())
    process.communicate()

def clear_temp_files(device_id: str) -> None:
    """Clears temporary files with the device ID as prefix."""
    for filename in os.listdir(TEMP_DIR):
        if filename.startswith(device_id):
            try:
                os.remove(os.path.join(TEMP_DIR, filename))
            except OSError as e:
                logging.error(f"Cannot remove file {filename}: {e}")

def get_user_data() -> Tuple[Optional[str], Optional[str]]:
    """Fetches user data from a data file, with fallback handling."""
    try:
        return DataExtractor.fetch_data()
    except Exception as e:
        logging.error(f"Error fetching data, attempting fallback: {e}")
        DataExtractor.fetch_datas()
        return DataExtractor.fetch_data()

# Shortcuts for common actions
def detect_img(manager: UserManager, img_name: str) -> bool:
    """Shortcut for detecting an image on the screen."""
    return manager.image_detector.detect(manager.adb_handler.get_cv2_image(), img_name)

def click_xy(manager: UserManager, x: int, y: int, delay: float = 0.5):
    """Shortcut for clicking a position with optional delay."""
    manager.click(x, y)
    time.sleep(delay)

def adb_cmd(manager: UserManager, command: str):
    """Shortcut for sending an ADB shell command."""
    manager.adb_handler.shell(command)

def evolve_pokemon(manager: UserManager):
    """Handles the sequence for evolving a Pokémon."""
    actions = [(200, 750), (730, 1902), (350, 2000), (230, 1760), (550, 1250), (550, 1914), (550, 1914)]
    for x, y in actions:
        click_xy(manager, x, y)
    adb_cmd(manager, 'input keyevent 4')
    adb_cmd(manager, 'input keyevent 4')

def dynamax_adventure(manager: UserManager, user: str):
    """Handles Dynamax adventure logic."""
    logging.info(f"Starting Dynamax adventure for {user}")
    manager._menu()

    if "exit" in manager.get_screen_text():
        adb_cmd(manager, 'input keyevent 4')

    click_xy(manager, 550, 1914)
    click_xy(manager, 280, 1680)
    click_xy(manager, 350, 350)
    adb_cmd(manager, 'input text "dynamax"')
    time.sleep(1)

    if not detect_img(manager, '0poke.png'):
        evolve_pokemon(manager)
        manager._menu()
        logging.info(f"No Dynamax adventure for {user}")
        return

    while not detect_img(manager, "pokebas.png"):
        manager.click_img("nex.png")
        logging.info(f"Looking for Dynamax adventure for {user}")
        time.sleep(7)

    evolve_pokemon(manager)

    while not "MP" in manager.get_screen_text():
        manager._menu()
        manager.click_img("nex.png")
        time.sleep(10)
        click_xy(manager, 540, 770)
        time.sleep(2)
        click_xy(manager, 540, 1350)
        logging.info("Adventure step.")
        time.sleep(2)

    if "Defeat" in manager.get_screen_text():
        click_xy(manager, 540, 1940)
    time.sleep(2)
    click_xy(manager, 540, 1350)
    time.sleep(2)
    click_xy(manager, 540, 1640)
    time.sleep(2)
    click_xy(manager, 540, 1350)

    while not "SKIP" in manager.get_screen_text():
        time.sleep(2)
        if detect_img(manager, 'pokeraid.png'):
            break
        logging.info("Looking for SKIP button")

    click_xy(manager, 540, 1887)
    click_xy(manager, 540, 1221)

    location_handler = LocationHandler(manager.device_id)
    location_handler.telegram_bot_sendtext(f"Dynamax adventure completed for {user}")

    screen = manager.screenshot_handler.capture()
    send_image(screen, adb=manager.adb_handler)

def caminar(manager: UserManager, user: str):
    if not detect_img(manager, "munequito.png") or not detect_img(manager, "munequito2.png"):
       manager.click_img("pgshars10.png")

    # Detectar y manejar el AutoWalk
   
    try:
            x, y = detect_img(manager, "munequito.png")
            j=60     
            if True :
                print("intentando")
                print(manager.get_screen_text())
                manager.click(x + j, y)
                time.sleep(3)
                j=j+10
            if "AutoWalk" in manager.get_screen_text():
                manager.click(200, 200)
            if True:
                manager.click(550, 900)
            print(manager.get_screen_text())
               
    except:
            pass

def curar(manager: UserManager):       
    manager.click_img("pkbss10.png")
    time.sleep(2)
    manager.click(616, 1200)
    time.sleep(2)
    if detect_img(manager,  "curars10.png"):
        
     manager.click_img("curars10.png")
     manager.click(350, 1200)
    else:
      manager.click(350, 1350) 
      manager.click_img("pkbss10.png")
      time.sleep(2)
      manager.click(150, 1180) 
      time.sleep(2)
      manager.click(150, 550) 
      time.sleep(2)
      manager.click_img("evolve.png")
      manager.click_img("evolve2.png")
      manager.click_img("evolve3.png")
      
      manager.click(350, 1250) 
      manager.click(350, 800) 
      time.sleep(7)
      manager.click(350, 1350)
      manager.click(350, 1350)
      manager.click(350, 1350)

def galar_adventure2(manager: UserManager, user: str):
    """Handles Galar adventure logic - versión principal que funciona."""
    manager.proxy_quit()
    print(manager.get_screen_text())
    manager.adb_handler.shell('input keyevent 4')

    logging.info(f"Starting Galar adventure for {user}")
    manager._menu()
    manager._menu()
    manager._menu()
    manager._menu()
    manager._menu()
    manager._menu()
    manager.adb_handler.shell('input keyevent 4')
    manager.adb_handler.shell('input keyevent 4')
    manager.adb_handler.shell('input keyevent 4')
    manager.adb_handler.shell('input keyevent 4')
    manager.adb_handler.shell('input keyevent 4')
    manager._menu()
    manager._menu()
    manager.adb_handler.shell('input keyevent 4')
    manager.adb_handler.shell('input keyevent 4')
    manager.adb_handler.shell('input keyevent 4')
    manager.adb_handler.shell('input keyevent 4')
    time.sleep(3)

    curar(manager)
    print(manager.get_screen_text())
    manager.click(350, 1333)
    if "CANCEL" in manager.get_screen_text():
     manager.adb_handler.shell('input keyevent 4')
    manager.budy()

    manager._menu()

    manager.click_img("pgshars10.png")
    manager.click(262, 369)
    time.sleep(2)
    if "Quest" in manager.get_screen_text():
        manager.click(400, 300)
        manager.click(509, 427)
    time.sleep(5)

    manager.click(330, 364)
    time.sleep(1)

    x,y=manager.get_screen_text_coord('Teleport')
    manager.click(x, y)

    manager._menu()
    manager._menu()
    manager.adb_handler.shell('input keyevent 4')
    manager.adb_handler.shell('input keyevent 4')
    manager.adb_handler.shell('input keyevent 4')
    manager.adb_handler.shell('input keyevent 4')
    time.sleep(3)

    manager.budy()

    manager.adb_handler.shell('input keyevent 4')
    print(manager.get_screen_text())
    if "CANCEL" in manager.get_screen_text():
     manager.adb_handler.shell('input keyevent 4')

    while True:
     if "location" in manager.get_screen_text():
         return
     if "GROUP" in manager.get_screen_text() :
         time.sleep(4)
     if detect_img(manager,  "x10.png"):
      if detect_img(manager,  "x11.png"):
          manager.adb_handler.shell('input keyevent 4')

     manager.click(341, 918)
     manager.click(341, 1050)
     manager.click(347, 746)
     manager.click(347, 816)
     manager.click(257, 1300)
     manager.click(350, 1000)
     manager.click(350, 1050)
     manager.click(350, 1100)
     manager.click(350, 1150)
     manager.click(530, 1200)
     manager.budy()

     if "Friend List" in manager.get_screen_text():
         manager.adb_handler.shell('input keyevent 4')
         manager.adb_handler.shell('input keyevent 4')
         manager.adb_handler.shell('input keyevent 4')
         manager.adb_handler.shell('input keyevent 4')

     if "Failed" in manager.get_screen_coords(160,155,250,200):
      return
     if detect_img(manager,  "name.png"):
         print("####################")
         print(manager.get_screen_coords(470,170,705,238))
         nam = manager.get_screen_coords(470,170,705,238)

     if "pack of" in manager.get_screen_text():
         return
     if  detect_img(manager,  "exch10.png"):
         return

     if  detect_img(manager,  "mora.png"):
         break
     if  detect_img(manager,  "raid10.png"):
         manager.click_img("bonus10.png")
         manager.click_img("bonus10.png")
         break

    while not detect_img(manager,  "pkbss10.png"):
                 manager.click_img("chall.png")
                 manager.click_img("bonus10.png")
                 time.sleep(2)
                 if detect_img(manager,  "mora.png"):
                  manager.click_img("pgshars10.png")
                  manager.click_img("pgshars10.png")
                  if detect_img(manager,  "shiny70.png"):
                      screen = manager.screenshot_handler()
                      telegram_bot_sendtext(f"shiny raid {user}")
                      send_image(screen, adb=manager.adb_handler)
                      if 'nam' in locals():
                       telegram_bot_sendtext(f"Shiny in {user} {nam}")
                      else:
                          telegram_bot_sendtext(f"Shiny in {user}")
                  else:
                      time.sleep(3)
                      if detect_img(manager,  "shin.png"):
                          screen = manager.screenshot_handler()
                          send_image(screen, adb=manager.adb_handler)
                          if 'nam' in locals():
                           telegram_bot_sendtext(f"Shiny in {user} {nam}")
                          else:
                              telegram_bot_sendtext(f"Shiny in {user}")

                  for _ in range(20):  # Realizar 20 swipes hacia arriba
                     manager.adb_handler.shell('input swipe 370 1218 370 200 200')
                     if detect_img(manager,  "pkbss10.png"):
                        return
                     time.sleep(3)
                  return

def handle_adventures(user: str, device_id: str):
    """Main handler for all adventures."""
    manager = UserManager(device_id)
    # Galar Adventure
    galar_adventure2(manager, user)

import os

log_file_path = r"c:\\temp\log.txt"

def delete_log_entry(user, password):
    """
    Elimina la línea que contiene user:password en el archivo de log.
    """
    updated_lines = []

    # Leer todas las líneas y filtrar la que coincide
    with open(log_file_path, 'r', encoding='utf-8') as file:
        updated_lines = [line for line in file if not line.startswith(f"{user}:{password}:")]

    # Sobrescribir el archivo sin la línea eliminada
    with open(log_file_path, 'w', encoding='utf-8') as file:
        file.writelines(updated_lines)

def main():
    if len(sys.argv) > 1:
        device_id = sys.argv[1]
        logging.info(f"Starting script for device: {device_id}")
    else:
        logging.error("No device ID provided. Using default.")
        device_id="************:5555"

    clear_temp_files(device_id)
    user, password = get_user_data()

    escaper = ShellInputEscaper()
    password = escaper.generate_shell_command(password)

    print(user, password)
    if user and password:
        manager = UserManager(device_id)
        with open(r"c:\\temp\log.txt", 'a') as file:
             file.write(f"{user}:{password}:test:{datetime.datetime.now()}\n")
        manager.adb_handler.shell("content insert --uri content://settings/system --bind name:s:accelerometer_rotation --bind value:i:0")
        manager.adb_handler.shell("content insert --uri content://settings/system --bind name:s:user_rotation --bind value:i:0")

        manager.cookies()

        if manager.login(user, password):
            logging.info(f"Login successful for {user}")
            manager.bk(user)
            delete_log_entry(user, password)
        else:
            with open(r"c:\\temp\log.txt", 'a') as file:
                 file.write(f"{user}:{password}:fallo:{datetime.datetime.now()}\n")
            logging.error(f"Login failed for {user}")
    else:
        logging.error("Invalid user data. Exiting.")

if __name__ == "__main__":
    while True:
     main()
