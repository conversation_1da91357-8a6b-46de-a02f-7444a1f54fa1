"""Abstract interface for device control operations."""

from abc import ABC, abstractmethod
from typing import Optional, List
import numpy as np

from ..entities.device import Device, DeviceInfo
from ..entities.automation import Coordinates


class IDeviceController(ABC):
    """Interface for controlling Android devices via ADB."""
    
    @abstractmethod
    async def list_devices(self) -> List[Device]:
        """List all available devices."""
        pass
    
    @abstractmethod
    async def connect(self, device_id: str) -> Device:
        """Connect to a device and return device information."""
        pass
    
    @abstractmethod
    async def disconnect(self, device_id: str) -> None:
        """Disconnect from a device."""
        pass
    
    @abstractmethod
    async def get_device_info(self, device_id: str) -> DeviceInfo:
        """Get detailed device information."""
        pass
    
    @abstractmethod
    async def take_screenshot(self, device_id: str) -> np.ndarray:
        """Take a screenshot and return as numpy array."""
        pass
    
    @abstractmethod
    async def click(self, device_id: str, coordinates: Coordinates) -> None:
        """Perform a click at specified coordinates."""
        pass
    
    @abstractmethod
    async def swipe(
        self, 
        device_id: str, 
        start: Coordinates, 
        end: Coordinates, 
        duration_ms: int = 500
    ) -> None:
        """Perform a swipe gesture."""
        pass
    
    @abstractmethod
    async def type_text(self, device_id: str, text: str) -> None:
        """Type text on the device."""
        pass
    
    @abstractmethod
    async def press_key(self, device_id: str, key_code: int) -> None:
        """Press a key by key code."""
        pass
    
    @abstractmethod
    async def execute_shell_command(self, device_id: str, command: str) -> str:
        """Execute a shell command on the device."""
        pass
    
    @abstractmethod
    async def is_device_connected(self, device_id: str) -> bool:
        """Check if device is connected."""
        pass
