"""Telegram notification implementation."""

import asyncio
from typing import Dict, Any, Optional
import httpx
import structlog

from ...core.interfaces.notifier import INotifier


logger = structlog.get_logger(__name__)


class TelegramNotifier(INotifier):
    """Telegram Bot API implementation of notifier interface."""
    
    def __init__(self, bot_token: str, chat_id: str):
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.base_url = f"https://api.telegram.org/bot{bot_token}"
        self._client: Optional[httpx.AsyncClient] = None
    
    async def _get_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client."""
        if self._client is None:
            self._client = httpx.AsyncClient(timeout=30.0)
        return self._client
    
    async def send_message(self, message: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Send a message notification."""
        
        try:
            client = await self._get_client()
            
            # Format message with metadata if provided
            formatted_message = await self._format_message(message, metadata)
            
            # Send message via Telegram API
            response = await client.post(
                f"{self.base_url}/sendMessage",
                json={
                    "chat_id": self.chat_id,
                    "text": formatted_message,
                    "parse_mode": "Markdown"
                }
            )
            
            if response.status_code == 200:
                logger.debug("Telegram message sent successfully")
                return True
            else:
                logger.error(
                    "Failed to send Telegram message",
                    status_code=response.status_code,
                    response=response.text
                )
                return False
                
        except Exception as e:
            logger.error("Telegram message send failed", error=str(e))
            return False
    
    async def send_success_notification(
        self,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Send a success notification."""
        
        success_message = f"✅ **SUCCESS**\n{message}"
        return await self.send_message(success_message, metadata)
    
    async def send_error_notification(
        self,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Send an error notification."""
        
        error_message = f"❌ **ERROR**\n{message}"
        return await self.send_message(error_message, metadata)
    
    async def send_warning_notification(
        self,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Send a warning notification."""
        
        warning_message = f"⚠️ **WARNING**\n{message}"
        return await self.send_message(warning_message, metadata)
    
    async def send_info_notification(
        self,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Send an info notification."""
        
        info_message = f"ℹ️ **INFO**\n{message}"
        return await self.send_message(info_message, metadata)
    
    async def send_automation_started(
        self,
        user_name: str,
        session_id: str,
        device_id: str
    ) -> bool:
        """Send automation started notification."""
        
        message = f"🎮 **Automation Started**\n"
        message += f"User: `{user_name}`\n"
        message += f"Session: `{session_id[:8]}...`\n"
        message += f"Device: `{device_id}`"
        
        return await self.send_message(message)
    
    async def send_automation_completed(
        self,
        user_name: str,
        session_id: str,
        duration_seconds: float,
        actions_count: int,
        success_rate: float
    ) -> bool:
        """Send automation completed notification."""
        
        message = f"🏆 **Automation Completed**\n"
        message += f"User: `{user_name}`\n"
        message += f"Session: `{session_id[:8]}...`\n"
        message += f"Duration: `{duration_seconds:.1f}s`\n"
        message += f"Actions: `{actions_count}`\n"
        message += f"Success Rate: `{success_rate:.1%}`"
        
        return await self.send_message(message)
    
    async def send_automation_failed(
        self,
        user_name: str,
        session_id: str,
        error_message: str
    ) -> bool:
        """Send automation failed notification."""
        
        message = f"💥 **Automation Failed**\n"
        message += f"User: `{user_name}`\n"
        message += f"Session: `{session_id[:8]}...`\n"
        message += f"Error: `{error_message}`"
        
        return await self.send_message(message)
    
    async def send_device_status(
        self,
        device_id: str,
        status: str,
        additional_info: Optional[str] = None
    ) -> bool:
        """Send device status notification."""
        
        status_emoji = {
            "connected": "🔗",
            "disconnected": "🔌",
            "error": "⚠️",
            "unknown": "❓"
        }.get(status.lower(), "📱")
        
        message = f"{status_emoji} **Device Status**\n"
        message += f"Device: `{device_id}`\n"
        message += f"Status: `{status}`"
        
        if additional_info:
            message += f"\nInfo: `{additional_info}`"
        
        return await self.send_message(message)
    
    async def send_system_health(
        self,
        healthy: bool,
        components_status: Dict[str, bool],
        active_sessions: int
    ) -> bool:
        """Send system health notification."""
        
        health_emoji = "💚" if healthy else "💔"
        message = f"{health_emoji} **System Health Check**\n"
        message += f"Overall Status: `{'Healthy' if healthy else 'Unhealthy'}`\n"
        message += f"Active Sessions: `{active_sessions}`\n\n"
        
        message += "**Components:**\n"
        for component, status in components_status.items():
            status_emoji = "✅" if status else "❌"
            message += f"{status_emoji} {component}\n"
        
        return await self.send_message(message)
    
    async def _format_message(
        self,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Format message with metadata."""
        
        formatted = message
        
        if metadata:
            formatted += "\n\n**Details:**\n"
            for key, value in metadata.items():
                # Format key to be more readable
                readable_key = key.replace('_', ' ').title()
                formatted += f"• {readable_key}: `{value}`\n"
        
        return formatted
    
    async def test_connection(self) -> bool:
        """Test Telegram bot connection."""
        
        try:
            client = await self._get_client()
            
            response = await client.get(f"{self.base_url}/getMe")
            
            if response.status_code == 200:
                bot_info = response.json()
                logger.debug(
                    "Telegram bot connection test successful",
                    bot_name=bot_info.get("result", {}).get("first_name", "Unknown")
                )
                return True
            else:
                logger.error(
                    "Telegram bot connection test failed",
                    status_code=response.status_code
                )
                return False
                
        except Exception as e:
            logger.error("Telegram connection test failed", error=str(e))
            return False
    
    async def health_check(self) -> dict:
        """Perform health check of Telegram notifier."""
        
        try:
            # Test connection
            connection_ok = await self.test_connection()
            
            if connection_ok:
                # Test sending a message
                test_message = "🔧 Health check test message"
                message_sent = await self.send_message(test_message)
                
                return {
                    "healthy": connection_ok and message_sent,
                    "connection_ok": connection_ok,
                    "message_sent": message_sent,
                    "chat_id": self.chat_id
                }
            else:
                return {
                    "healthy": False,
                    "connection_ok": False,
                    "error": "Failed to connect to Telegram API"
                }
                
        except Exception as e:
            logger.error("Telegram notifier health check failed", error=str(e))
            return {
                "healthy": False,
                "error": str(e)
            }
    
    async def close(self) -> None:
        """Close HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None
