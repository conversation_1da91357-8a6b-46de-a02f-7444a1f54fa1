"""Tesseract OCR implementation."""

import pytesseract
import cv2
import numpy as np
from typing import List, Optional, Dict, Any
import structlog
from pathlib import Path

from ...core.interfaces.ocr_reader import IOCRReader
from ...core.entities.automation import Coordinates


logger = structlog.get_logger(__name__)


class TesseractOCRReader(IOCRReader):
    """Tesseract implementation of OCR reader interface."""
    
    def __init__(self, tesseract_path: Optional[str] = None):
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # Default OCR configuration
        self.default_config = '--oem 3 --psm 6'
    
    async def extract_text(
        self,
        image: np.ndarray,
        language: str = 'eng',
        config: Optional[str] = None
    ) -> List[str]:
        """Extract text from image."""
        
        try:
            # Use default config if none provided
            ocr_config = config or self.default_config
            
            # Preprocess image for better OCR results
            processed_image = await self._preprocess_for_ocr(image)
            
            # Extract text
            text = pytesseract.image_to_string(
                processed_image,
                lang=language,
                config=ocr_config
            )
            
            # Clean and split text into lines
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            
            logger.debug(
                "Text extracted",
                language=language,
                lines_count=len(lines),
                config=ocr_config
            )
            
            return lines
            
        except Exception as e:
            logger.error("Text extraction failed", language=language, error=str(e))
            return []
    
    async def extract_text_with_confidence(
        self,
        image: np.ndarray,
        language: str = 'eng',
        min_confidence: float = 60.0
    ) -> List[Dict[str, Any]]:
        """Extract text with confidence scores."""
        
        try:
            # Preprocess image
            processed_image = await self._preprocess_for_ocr(image)
            
            # Get detailed OCR data
            data = pytesseract.image_to_data(
                processed_image,
                lang=language,
                config=self.default_config,
                output_type=pytesseract.Output.DICT
            )
            
            # Filter results by confidence
            results = []
            for i in range(len(data['text'])):
                confidence = float(data['conf'][i])
                text = data['text'][i].strip()
                
                if confidence >= min_confidence and text:
                    results.append({
                        'text': text,
                        'confidence': confidence,
                        'coordinates': Coordinates(
                            x=data['left'][i] + data['width'][i] // 2,
                            y=data['top'][i] + data['height'][i] // 2
                        ),
                        'bbox': {
                            'left': data['left'][i],
                            'top': data['top'][i],
                            'width': data['width'][i],
                            'height': data['height'][i]
                        }
                    })
            
            logger.debug(
                "Text extracted with confidence",
                language=language,
                min_confidence=min_confidence,
                results_count=len(results)
            )
            
            return results
            
        except Exception as e:
            logger.error(
                "Text extraction with confidence failed",
                language=language,
                error=str(e)
            )
            return []
    
    async def find_text_coordinates(
        self,
        image: np.ndarray,
        search_text: str,
        language: str = 'eng'
    ) -> Optional[Coordinates]:
        """Find coordinates of specific text in image."""
        
        try:
            # Get text with coordinates
            text_data = await self.extract_text_with_confidence(image, language)
            
            # Search for the text
            for item in text_data:
                if search_text.lower() in item['text'].lower():
                    logger.debug(
                        "Text found",
                        search_text=search_text,
                        found_text=item['text'],
                        coordinates=item['coordinates']
                    )
                    return item['coordinates']
            
            logger.debug("Text not found", search_text=search_text)
            return None
            
        except Exception as e:
            logger.error(
                "Text coordinate search failed",
                search_text=search_text,
                error=str(e)
            )
            return None
    
    async def extract_numbers(
        self,
        image: np.ndarray,
        language: str = 'eng'
    ) -> List[int]:
        """Extract numbers from image."""
        
        try:
            # Use digit-only configuration
            config = '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789'
            
            # Preprocess image
            processed_image = await self._preprocess_for_ocr(image)
            
            # Extract text
            text = pytesseract.image_to_string(
                processed_image,
                lang=language,
                config=config
            )
            
            # Extract numbers
            numbers = []
            for line in text.split('\n'):
                line = line.strip()
                if line.isdigit():
                    numbers.append(int(line))
            
            logger.debug("Numbers extracted", count=len(numbers), numbers=numbers)
            return numbers
            
        except Exception as e:
            logger.error("Number extraction failed", error=str(e))
            return []
    
    async def validate_text_presence(
        self,
        image: np.ndarray,
        expected_texts: List[str],
        language: str = 'eng'
    ) -> Dict[str, bool]:
        """Validate presence of expected texts in image."""
        
        try:
            # Extract all text
            extracted_text = await self.extract_text(image, language)
            full_text = ' '.join(extracted_text).lower()
            
            # Check each expected text
            results = {}
            for expected in expected_texts:
                results[expected] = expected.lower() in full_text
            
            logger.debug(
                "Text presence validated",
                expected_count=len(expected_texts),
                found_count=sum(results.values())
            )
            
            return results
            
        except Exception as e:
            logger.error("Text presence validation failed", error=str(e))
            return {text: False for text in expected_texts}
    
    async def _preprocess_for_ocr(self, image: np.ndarray) -> np.ndarray:
        """Preprocess image for better OCR results."""
        
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # Apply denoising
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # Apply threshold
            _, thresh = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Apply morphological operations to clean up
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            return cleaned
            
        except Exception as e:
            logger.error("OCR preprocessing failed", error=str(e))
            return image
    
    async def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        
        try:
            languages = pytesseract.get_languages()
            logger.debug("Supported languages retrieved", count=len(languages))
            return languages
            
        except Exception as e:
            logger.error("Failed to get supported languages", error=str(e))
            return ['eng']  # Default fallback
    
    async def health_check(self) -> dict:
        """Perform health check of OCR reader."""
        
        try:
            # Test basic OCR functionality
            test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255
            cv2.putText(test_image, 'TEST', (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            
            # Try to extract text
            text = pytesseract.image_to_string(test_image)
            ocr_working = 'TEST' in text.upper()
            
            # Get version info
            try:
                version = pytesseract.get_tesseract_version()
            except:
                version = "Unknown"
            
            # Get supported languages
            languages = await self.get_supported_languages()
            
            return {
                "healthy": ocr_working,
                "tesseract_version": str(version),
                "supported_languages_count": len(languages),
                "test_extraction_successful": ocr_working
            }
            
        except Exception as e:
            logger.error("OCR reader health check failed", error=str(e))
            return {
                "healthy": False,
                "error": str(e)
            }
