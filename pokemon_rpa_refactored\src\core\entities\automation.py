"""Automation entities for RPA operations."""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any
from uuid import uuid4


class AutomationStatus(Enum):
    """Status of automation execution."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class ActionType(Enum):
    """Types of automation actions."""
    CLICK = "click"
    SWIPE = "swipe"
    TYPE_TEXT = "type_text"
    WAIT = "wait"
    SCREENSHOT = "screenshot"
    DETECT_IMAGE = "detect_image"
    OCR_READ = "ocr_read"


@dataclass(frozen=True)
class Coordinates:
    """Screen coordinates."""
    x: int
    y: int
    
    def __post_init__(self):
        if self.x < 0 or self.y < 0:
            raise ValueError("Coordinates must be non-negative")


@dataclass(frozen=True)
class DetectionResult:
    """Result of image detection operation."""
    found: bool
    confidence: float
    coordinates: Optional[Coordinates] = None
    template_name: Optional[str] = None
    
    def __post_init__(self):
        if not 0.0 <= self.confidence <= 1.0:
            raise ValueError("Confidence must be between 0.0 and 1.0")


@dataclass
class AutomationAction:
    """Individual automation action."""
    action_id: str = field(default_factory=lambda: str(uuid4()))
    action_type: ActionType = ActionType.CLICK
    parameters: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    result: Optional[Any] = None
    error: Optional[str] = None
    duration_ms: Optional[int] = None
    
    @property
    def is_successful(self) -> bool:
        """Check if action was successful."""
        return self.error is None


@dataclass
class AutomationSession:
    """Automation session containing multiple actions."""
    
    session_id: str = field(default_factory=lambda: str(uuid4()))
    device_id: str = ""
    user_name: str = ""
    session_type: str = ""
    status: AutomationStatus = AutomationStatus.PENDING
    actions: List[AutomationAction] = field(default_factory=list)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def start(self) -> None:
        """Start the automation session."""
        self.status = AutomationStatus.RUNNING
        self.started_at = datetime.now()
    
    def complete(self) -> None:
        """Mark session as completed."""
        self.status = AutomationStatus.COMPLETED
        self.completed_at = datetime.now()
    
    def fail(self, error_message: str) -> None:
        """Mark session as failed."""
        self.status = AutomationStatus.FAILED
        self.error_message = error_message
        self.completed_at = datetime.now()
    
    def add_action(self, action: AutomationAction) -> None:
        """Add an action to the session."""
        self.actions.append(action)
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Get session duration in seconds."""
        if not self.started_at or not self.completed_at:
            return None
        return (self.completed_at - self.started_at).total_seconds()
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate of actions."""
        if not self.actions:
            return 0.0
        successful = sum(1 for action in self.actions if action.is_successful)
        return successful / len(self.actions)


@dataclass
class GameState:
    """Current state of the game."""
    
    screen_name: str = "unknown"
    detected_elements: List[DetectionResult] = field(default_factory=list)
    ocr_text: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def has_element(self, template_name: str) -> bool:
        """Check if specific element is detected."""
        return any(
            result.template_name == template_name and result.found
            for result in self.detected_elements
        )
    
    def get_element_coordinates(self, template_name: str) -> Optional[Coordinates]:
        """Get coordinates of detected element."""
        for result in self.detected_elements:
            if result.template_name == template_name and result.found:
                return result.coordinates
        return None
