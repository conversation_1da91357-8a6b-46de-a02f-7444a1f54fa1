"""Prometheus metrics collection for Pokemon RPA Framework."""

import time
import psutil
import asyncio
from typing import Dict, Any, Optional
from prometheus_client import (
    Counter, Histogram, Gauge, Info, 
    CollectorRegistry, generate_latest,
    CONTENT_TYPE_LATEST
)
from structlog import get_logger

logger = get_logger(__name__)


class MetricsCollector:
    """Centralized metrics collection for the Pokemon RPA Framework."""
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        """Initialize metrics collector with optional custom registry."""
        self.registry = registry or CollectorRegistry()
        self._setup_metrics()
        self._last_collection_time = time.time()
    
    def _setup_metrics(self):
        """Setup all Prometheus metrics."""
        # HTTP Request Metrics
        self.http_requests_total = Counter(
            'pokemon_rpa_http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status'],
            registry=self.registry
        )
        
        self.http_request_duration = Histogram(
            'pokemon_rpa_http_request_duration_seconds',
            'HTTP request duration in seconds',
            ['method', 'endpoint'],
            registry=self.registry
        )
        
        # Automation Session Metrics
        self.automation_sessions_total = Counter(
            'pokemon_rpa_automation_sessions_total',
            'Total automation sessions',
            ['status', 'session_type'],
            registry=self.registry
        )
        
        self.active_automation_sessions = Gauge(
            'pokemon_rpa_active_automation_sessions',
            'Currently active automation sessions',
            registry=self.registry
        )
        
        self.automation_session_duration = Histogram(
            'pokemon_rpa_automation_session_duration_seconds',
            'Automation session duration in seconds',
            ['session_type', 'status'],
            registry=self.registry
        )
        
        # Device Metrics
        self.connected_devices = Gauge(
            'pokemon_rpa_connected_devices',
            'Number of connected devices',
            registry=self.registry
        )
        
        self.device_connection_failures = Counter(
            'pokemon_rpa_device_connection_failures_total',
            'Total device connection failures',
            ['device_id', 'error_type'],
            registry=self.registry
        )
        
        self.device_operations_total = Counter(
            'pokemon_rpa_device_operations_total',
            'Total device operations',
            ['operation', 'status'],
            registry=self.registry
        )
        
        # Computer Vision Metrics
        self.ocr_requests_total = Counter(
            'pokemon_rpa_ocr_requests_total',
            'Total OCR requests',
            ['language', 'status'],
            registry=self.registry
        )
        
        self.ocr_duration = Histogram(
            'pokemon_rpa_ocr_duration_seconds',
            'OCR processing duration in seconds',
            ['language'],
            registry=self.registry
        )
        
        self.ocr_failures_total = Counter(
            'pokemon_rpa_ocr_failures_total',
            'Total OCR failures',
            ['language', 'error_type'],
            registry=self.registry
        )
        
        self.image_detection_requests_total = Counter(
            'pokemon_rpa_image_detection_requests_total',
            'Total image detection requests',
            ['template', 'status'],
            registry=self.registry
        )
        
        self.image_detection_duration = Histogram(
            'pokemon_rpa_image_detection_duration_seconds',
            'Image detection duration in seconds',
            ['template'],
            registry=self.registry
        )
        
        # System Resource Metrics
        self.cpu_usage_percent = Gauge(
            'pokemon_rpa_cpu_usage_percent',
            'CPU usage percentage',
            registry=self.registry
        )
        
        self.memory_usage_percent = Gauge(
            'pokemon_rpa_memory_usage_percent',
            'Memory usage percentage',
            registry=self.registry
        )
        
        self.disk_usage_percent = Gauge(
            'pokemon_rpa_disk_usage_percent',
            'Disk usage percentage',
            registry=self.registry
        )
        
        # Database Metrics
        self.database_operations_total = Counter(
            'pokemon_rpa_database_operations_total',
            'Total database operations',
            ['operation', 'collection', 'status'],
            registry=self.registry
        )
        
        self.database_operation_duration = Histogram(
            'pokemon_rpa_database_operation_duration_seconds',
            'Database operation duration in seconds',
            ['operation', 'collection'],
            registry=self.registry
        )
        
        self.database_connections = Gauge(
            'pokemon_rpa_database_connections',
            'Active database connections',
            registry=self.registry
        )
        
        # Notification Metrics
        self.notification_requests_total = Counter(
            'pokemon_rpa_notification_requests_total',
            'Total notification requests',
            ['type', 'status'],
            registry=self.registry
        )
        
        self.notification_failures_total = Counter(
            'pokemon_rpa_notification_failures_total',
            'Total notification failures',
            ['type', 'error_type'],
            registry=self.registry
        )
        
        # Application Info
        self.app_info = Info(
            'pokemon_rpa_app_info',
            'Application information',
            registry=self.registry
        )
        
        # Health Check Metrics
        self.health_check_status = Gauge(
            'pokemon_rpa_health_check_status',
            'Health check status (1=healthy, 0=unhealthy)',
            ['component'],
            registry=self.registry
        )
        
        # Error Metrics
        self.errors_total = Counter(
            'pokemon_rpa_errors_total',
            'Total errors by type',
            ['error_type', 'component'],
            registry=self.registry
        )
    
    def set_app_info(self, version: str, build_date: str, commit_hash: str):
        """Set application information."""
        self.app_info.info({
            'version': version,
            'build_date': build_date,
            'commit_hash': commit_hash,
            'python_version': psutil.Process().exe()
        })
    
    def record_http_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record HTTP request metrics."""
        status_class = f"{status_code // 100}xx"
        self.http_requests_total.labels(
            method=method,
            endpoint=endpoint,
            status=status_class
        ).inc()
        
        self.http_request_duration.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
    
    def record_automation_session_start(self, session_type: str):
        """Record automation session start."""
        self.automation_sessions_total.labels(
            status='started',
            session_type=session_type
        ).inc()
        self.active_automation_sessions.inc()
    
    def record_automation_session_end(self, session_type: str, status: str, duration: float):
        """Record automation session completion."""
        self.automation_sessions_total.labels(
            status=status,
            session_type=session_type
        ).inc()
        
        self.automation_session_duration.labels(
            session_type=session_type,
            status=status
        ).observe(duration)
        
        self.active_automation_sessions.dec()
    
    def update_connected_devices(self, count: int):
        """Update connected devices count."""
        self.connected_devices.set(count)
    
    def record_device_connection_failure(self, device_id: str, error_type: str):
        """Record device connection failure."""
        self.device_connection_failures.labels(
            device_id=device_id,
            error_type=error_type
        ).inc()
    
    def record_device_operation(self, operation: str, status: str):
        """Record device operation."""
        self.device_operations_total.labels(
            operation=operation,
            status=status
        ).inc()
    
    def record_ocr_request(self, language: str, status: str, duration: float):
        """Record OCR request metrics."""
        self.ocr_requests_total.labels(
            language=language,
            status=status
        ).inc()
        
        if status == 'success':
            self.ocr_duration.labels(language=language).observe(duration)
    
    def record_ocr_failure(self, language: str, error_type: str):
        """Record OCR failure."""
        self.ocr_failures_total.labels(
            language=language,
            error_type=error_type
        ).inc()
    
    def record_image_detection(self, template: str, status: str, duration: float):
        """Record image detection metrics."""
        self.image_detection_requests_total.labels(
            template=template,
            status=status
        ).inc()
        
        if status == 'success':
            self.image_detection_duration.labels(template=template).observe(duration)
    
    def record_database_operation(self, operation: str, collection: str, status: str, duration: float):
        """Record database operation metrics."""
        self.database_operations_total.labels(
            operation=operation,
            collection=collection,
            status=status
        ).inc()
        
        if status == 'success':
            self.database_operation_duration.labels(
                operation=operation,
                collection=collection
            ).observe(duration)
    
    def update_database_connections(self, count: int):
        """Update database connections count."""
        self.database_connections.set(count)
    
    def record_notification(self, notification_type: str, status: str):
        """Record notification metrics."""
        self.notification_requests_total.labels(
            type=notification_type,
            status=status
        ).inc()
    
    def record_notification_failure(self, notification_type: str, error_type: str):
        """Record notification failure."""
        self.notification_failures_total.labels(
            type=notification_type,
            error_type=error_type
        ).inc()
    
    def update_health_check(self, component: str, is_healthy: bool):
        """Update health check status."""
        self.health_check_status.labels(component=component).set(1 if is_healthy else 0)
    
    def record_error(self, error_type: str, component: str):
        """Record error occurrence."""
        self.errors_total.labels(
            error_type=error_type,
            component=component
        ).inc()
    
    async def collect_system_metrics(self):
        """Collect system resource metrics."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_usage_percent.set(cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.memory_usage_percent.set(memory.percent)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.disk_usage_percent.set(disk_percent)
            
            logger.debug(
                "System metrics collected",
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_percent=disk_percent
            )
            
        except Exception as e:
            logger.error("Failed to collect system metrics", error=str(e))
            self.record_error("system_metrics_collection", "metrics_collector")
    
    def get_metrics(self) -> str:
        """Get metrics in Prometheus format."""
        return generate_latest(self.registry).decode('utf-8')
    
    def get_content_type(self) -> str:
        """Get content type for metrics endpoint."""
        return CONTENT_TYPE_LATEST


class MetricsMiddleware:
    """Middleware for automatic metrics collection."""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
    
    async def __call__(self, request, call_next):
        """Process request and collect metrics."""
        start_time = time.time()
        
        try:
            response = await call_next(request)
            duration = time.time() - start_time
            
            # Extract request information
            method = request.method
            endpoint = str(request.url.path)
            status_code = response.status_code
            
            # Record metrics
            self.metrics.record_http_request(method, endpoint, status_code, duration)
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            
            # Record error metrics
            self.metrics.record_http_request(
                request.method,
                str(request.url.path),
                500,
                duration
            )
            self.metrics.record_error("http_request", "middleware")
            
            raise


# Global metrics instance
_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """Get global metrics collector instance."""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


def setup_metrics(version: str, build_date: str, commit_hash: str) -> MetricsCollector:
    """Setup and configure metrics collector."""
    global _metrics_collector
    _metrics_collector = MetricsCollector()
    _metrics_collector.set_app_info(version, build_date, commit_hash)
    
    logger.info(
        "Metrics collector initialized",
        version=version,
        build_date=build_date,
        commit_hash=commit_hash
    )
    
    return _metrics_collector


async def start_metrics_collection():
    """Start background metrics collection."""
    metrics = get_metrics_collector()
    
    async def collect_loop():
        while True:
            try:
                await metrics.collect_system_metrics()
                await asyncio.sleep(15)  # Collect every 15 seconds
            except Exception as e:
                logger.error("Error in metrics collection loop", error=str(e))
                await asyncio.sleep(30)  # Wait longer on error
    
    # Start background task
    asyncio.create_task(collect_loop())
    logger.info("Started background metrics collection")


# Decorator for timing functions
def timed_operation(operation_name: str, component: str):
    """Decorator to time operations and record metrics."""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            metrics = get_metrics_collector()
            
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Record successful operation
                metrics.record_database_operation(
                    operation_name, component, 'success', duration
                )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                # Record failed operation
                metrics.record_database_operation(
                    operation_name, component, 'failure', duration
                )
                metrics.record_error(type(e).__name__, component)
                
                raise
        
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            metrics = get_metrics_collector()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Record successful operation
                metrics.record_database_operation(
                    operation_name, component, 'success', duration
                )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                # Record failed operation
                metrics.record_database_operation(
                    operation_name, component, 'failure', duration
                )
                metrics.record_error(type(e).__name__, component)
                
                raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator
