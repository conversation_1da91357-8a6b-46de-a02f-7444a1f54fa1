"""Abstract interface for image detection operations."""

from abc import ABC, abstractmethod
from typing import List, Optional
import numpy as np

from ..entities.automation import DetectionR<PERSON><PERSON>, Coordinates


class IImageDetector(ABC):
    """Interface for computer vision and image detection."""
    
    @abstractmethod
    async def detect_template(
        self, 
        screenshot: np.ndarray, 
        template_path: str,
        threshold: float = 0.8
    ) -> DetectionResult:
        """Detect a template image in the screenshot."""
        pass
    
    @abstractmethod
    async def detect_multiple_templates(
        self, 
        screenshot: np.ndarray, 
        template_paths: List[str],
        threshold: float = 0.8
    ) -> List[DetectionResult]:
        """Detect multiple template images in the screenshot."""
        pass
    
    @abstractmethod
    async def find_text_regions(self, screenshot: np.ndarray) -> List[Coordinates]:
        """Find regions containing text in the screenshot."""
        pass
    
    @abstractmethod
    async def crop_region(
        self, 
        screenshot: np.ndarray, 
        top_left: Coordinates, 
        bottom_right: Coordinates
    ) -> np.ndarray:
        """Crop a specific region from the screenshot."""
        pass
    
    @abstractmethod
    async def save_screenshot(
        self, 
        screenshot: np.ndarray, 
        file_path: str
    ) -> None:
        """Save screenshot to file."""
        pass
    
    @abstractmethod
    async def load_template(self, template_path: str) -> np.ndarray:
        """Load template image from file."""
        pass
    
    @abstractmethod
    async def preprocess_image(
        self, 
        image: np.ndarray,
        grayscale: bool = True,
        blur: bool = False
    ) -> np.ndarray:
        """Preprocess image for better detection."""
        pass
