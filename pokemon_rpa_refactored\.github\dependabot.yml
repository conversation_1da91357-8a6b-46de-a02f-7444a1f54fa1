version: 2
updates:
  # Python dependencies
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "pokemon-rpa-team"
    assignees:
      - "pokemon-rpa-maintainer"
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "python"
    allow:
      - dependency-type: "direct"
      - dependency-type: "indirect"
    ignore:
      # Ignore major version updates for stable dependencies
      - dependency-name: "opencv-python"
        update-types: ["version-update:semver-major"]
      - dependency-name: "numpy"
        update-types: ["version-update:semver-major"]

  # Docker dependencies
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "pokemon-rpa-team"
    commit-message:
      prefix: "docker"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "wednesday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "pokemon-rpa-team"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
