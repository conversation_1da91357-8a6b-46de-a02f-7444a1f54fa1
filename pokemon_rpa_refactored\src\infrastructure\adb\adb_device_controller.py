"""ADB implementation of device controller interface."""

import asyncio
from typing import Optional, List
import numpy as np
import cv2
import structlog
from ppadb.client import Client
from ppadb.device import Device as ADBDevice

from ...core.interfaces.device_controller import IDeviceController
from ...core.entities.device import Device, DeviceInfo, DeviceStatus, DeviceType
from ...core.entities.automation import Coordinates


logger = structlog.get_logger(__name__)


class ADBDevice<PERSON>ontroller(IDeviceController):
    """ADB implementation of device controller."""
    
    def __init__(self, host: str = "localhost", port: int = 5037):
        self._host = host
        self._port = port
        self._client: Optional[Client] = None
        self._connected_devices: dict[str, ADBDevice] = {}
    
    async def list_devices(self) -> List[Device]:
        """List all available devices."""
        try:
            await self._ensure_client()
            
            devices = []
            adb_devices = self._client.devices()
            
            for adb_device in adb_devices:
                device_id = adb_device.serial
                
                # Determine device status
                status = DeviceStatus.CONNECTED if adb_device.get_state() == "device" else DeviceStatus.DISCONNECTED
                
                # Create Device entity
                device = Device(
                    device_id=device_id,
                    device_type=DeviceType.ANDROID,
                    status=status,
                    name=f"Android Device {device_id}",
                    model="Unknown",
                    android_version="Unknown"
                )
                
                devices.append(device)
                logger.debug(f"Found device: {device_id} ({status})")
            
            logger.info(f"Listed {len(devices)} devices")
            return devices
            
        except Exception as e:
            logger.error(f"Error listing devices: {e}")
            return []
    
    async def connect(self, device_id: str) -> Device:
        """Connect to a device and return device information."""
        try:
            logger.info("Connecting to device", device_id=device_id)
            
            # Initialize ADB client if needed
            if not self._client:
                self._client = Client(host=self._host, port=self._port)
            
            # Get device from ADB
            adb_device = self._client.device(device_id)
            if not adb_device:
                raise ConnectionError(f"Device {device_id} not found")
            
            # Store device connection
            self._connected_devices[device_id] = adb_device
            
            # Get device information
            device_info = await self.get_device_info(device_id)
            
            device = Device(
                device_id=device_id,
                status=DeviceStatus.CONNECTED,
                info=device_info
            )
            
            logger.info(
                "Successfully connected to device",
                device_id=device_id,
                model=device_info.model,
                android_version=device_info.android_version
            )
            
            return device
            
        except Exception as e:
            logger.error(
                "Failed to connect to device",
                device_id=device_id,
                error=str(e)
            )
            raise ConnectionError(f"Failed to connect to device {device_id}: {str(e)}")
    
    async def disconnect(self, device_id: str) -> None:
        """Disconnect from a device."""
        if device_id in self._connected_devices:
            del self._connected_devices[device_id]
            logger.info("Disconnected from device", device_id=device_id)
    
    async def get_device_info(self, device_id: str) -> DeviceInfo:
        """Get detailed device information."""
        adb_device = self._get_device(device_id)
        
        try:
            # Get device properties
            model = await self._execute_shell_command(device_id, "getprop ro.product.model")
            manufacturer = await self._execute_shell_command(device_id, "getprop ro.product.manufacturer")
            brand = await self._execute_shell_command(device_id, "getprop ro.product.brand")
            product = await self._execute_shell_command(device_id, "getprop ro.product.name")
            android_version = await self._execute_shell_command(device_id, "getprop ro.build.version.release")
            api_level_str = await self._execute_shell_command(device_id, "getprop ro.build.version.sdk")
            
            # Get screen properties
            wm_size = await self._execute_shell_command(device_id, "wm size")
            wm_density = await self._execute_shell_command(device_id, "wm density")
            
            # Parse screen size with robust handling of different formats
            screen_width, screen_height = self._parse_screen_size(wm_size)
            
            # Parse density with robust handling of different formats
            density = self._parse_density(wm_density)
            
            # Parse API level
            try:
                api_level = int(api_level_str.strip())
            except (ValueError, AttributeError):
                logger.warning(f"Could not parse API level from '{api_level_str}', using default")
                api_level = 0
            
            # Determine device type
            device_type = DeviceType.EMULATOR if "emulator" in device_id else DeviceType.PHONE
            
            return DeviceInfo(
                device_id=device_id,
                model=model.strip(),
                manufacturer=manufacturer.strip(),
                brand=brand.strip(),
                product=product.strip(),
                android_version=android_version.strip(),
                screen_width=screen_width,
                screen_height=screen_height,
                density=density,
                device_type=device_type,
                api_level=api_level
            )
            
        except Exception as e:
            logger.error(
                "Failed to get device info",
                device_id=device_id,
                error=str(e)
            )
            raise RuntimeError(f"Failed to get device info: {str(e)}")
    
    async def take_screenshot(self, device_id: str) -> np.ndarray:
        """Take a screenshot and return as numpy array."""
        adb_device = self._get_device(device_id)
        
        try:
            # Take screenshot
            screenshot_data = adb_device.screencap()
            
            # Convert to numpy array
            nparr = np.frombuffer(screenshot_data, np.uint8)
            screenshot = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if screenshot is None:
                raise RuntimeError("Failed to decode screenshot")
            
            logger.debug("Screenshot taken", device_id=device_id, shape=screenshot.shape)
            return screenshot
            
        except Exception as e:
            logger.error(
                "Failed to take screenshot",
                device_id=device_id,
                error=str(e)
            )
            raise RuntimeError(f"Failed to take screenshot: {str(e)}")
    
    async def click(self, device_id: str, coordinates: Coordinates) -> None:
        """Perform a click at specified coordinates."""
        try:
            command = f"input tap {coordinates.x} {coordinates.y}"
            await self._execute_shell_command(device_id, command)
            
            logger.debug(
                "Click performed",
                device_id=device_id,
                x=coordinates.x,
                y=coordinates.y
            )
            
        except Exception as e:
            logger.error(
                "Failed to perform click",
                device_id=device_id,
                coordinates=coordinates,
                error=str(e)
            )
            raise RuntimeError(f"Failed to perform click: {str(e)}")
    
    async def swipe(
        self, 
        device_id: str, 
        start: Coordinates, 
        end: Coordinates, 
        duration_ms: int = 500
    ) -> None:
        """Perform a swipe gesture."""
        try:
            command = f"input swipe {start.x} {start.y} {end.x} {end.y} {duration_ms}"
            await self._execute_shell_command(device_id, command)
            
            logger.debug(
                "Swipe performed",
                device_id=device_id,
                start=start,
                end=end,
                duration_ms=duration_ms
            )
            
        except Exception as e:
            logger.error(
                "Failed to perform swipe",
                device_id=device_id,
                start=start,
                end=end,
                error=str(e)
            )
            raise RuntimeError(f"Failed to perform swipe: {str(e)}")
    
    async def type_text(self, device_id: str, text: str) -> None:
        """Type text on the device."""
        try:
            # Escape special characters
            escaped_text = text.replace(" ", "%s").replace("'", "\\'")
            command = f"input text '{escaped_text}'"
            await self._execute_shell_command(device_id, command)
            
            logger.debug("Text typed", device_id=device_id, text=text)
            
        except Exception as e:
            logger.error(
                "Failed to type text",
                device_id=device_id,
                text=text,
                error=str(e)
            )
            raise RuntimeError(f"Failed to type text: {str(e)}")
    
    async def press_key(self, device_id: str, key_code: int) -> None:
        """Press a key by key code."""
        try:
            command = f"input keyevent {key_code}"
            await self._execute_shell_command(device_id, command)
            
            logger.debug("Key pressed", device_id=device_id, key_code=key_code)
            
        except Exception as e:
            logger.error(
                "Failed to press key",
                device_id=device_id,
                key_code=key_code,
                error=str(e)
            )
            raise RuntimeError(f"Failed to press key: {str(e)}")
    
    async def execute_shell_command(self, device_id: str, command: str) -> str:
        """Execute a shell command on the device."""
        return await self._execute_shell_command(device_id, command)
    
    async def is_device_connected(self, device_id: str) -> bool:
        """Check if device is connected."""
        try:
            await self._ensure_client()
            
            # Check if device is available in ADB
            adb_devices = self._client.devices()
            device_ids = [device.serial for device in adb_devices]
            
            if device_id not in device_ids:
                return False
            
            # Try a simple command to verify connection
            await self._execute_shell_command(device_id, "echo 'test'")
            
            # Update internal state if device is connected but not tracked
            if device_id not in self._connected_devices:
                adb_device = self._client.device(device_id)
                if adb_device:
                    self._connected_devices[device_id] = adb_device
            
            return True
            
        except Exception as e:
            logger.debug(f"Device connection check failed for {device_id}: {str(e)}")
            return False
    
    def _get_device(self, device_id: str) -> ADBDevice:
        """Get ADB device instance."""
        if not self._client:
            raise ConnectionError("ADB client not initialized")
        
        # Get device directly from ADB client instead of relying on cache
        adb_device = self._client.device(device_id)
        if not adb_device:
            raise ConnectionError(f"Device {device_id} not found in ADB")
        
        # Update cache for future use
        self._connected_devices[device_id] = adb_device
        
        return adb_device
    
    async def _execute_shell_command(self, device_id: str, command: str) -> str:
        """Execute shell command and return output."""
        await self._ensure_client()
        adb_device = self._get_device(device_id)
        
        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, 
                adb_device.shell, 
                command
            )
            
            return result if result else ""
            
        except Exception as e:
            logger.error(
                "Shell command failed",
                device_id=device_id,
                command=command,
                error=str(e)
            )
            raise RuntimeError(f"Shell command failed: {str(e)}")
    
    async def _ensure_client(self) -> None:
        """Ensure ADB client is initialized."""
        if not self._client:
            self._client = Client(host=self._host, port=self._port)
    
    def _parse_screen_size(self, output: str) -> tuple[int, int]:
        """Parse screen size from wm size output with robust handling of different formats."""
        try:
            # Clean the output
            output = output.strip()
            
            # Handle different formats:
            # Format 1: "Physical size: 1080x2340"
            # Format 2: "3040\nOverride size" (problematic case)
            # Format 3: "1080x2340"
            
            if ": " in output:
                # Standard format: "Physical size: 1080x2340"
                size_part = output.split(": ")[1].strip()
            else:
                # Handle cases like "3040\nOverride size" - take first line
                lines = output.split('\n')
                size_part = lines[0].strip()
            
            # Try to find dimensions in various formats
            if 'x' in size_part:
                # Format: "1080x2340"
                dimensions = size_part.split('x')
                if len(dimensions) == 2:
                    width = int(dimensions[0].strip())
                    height = int(dimensions[1].strip())
                    return width, height
            
            # If we can't parse, try to extract numbers from the string
            import re
            numbers = re.findall(r'\d+', output)
            if len(numbers) >= 2:
                # Take first two numbers as width and height
                width = int(numbers[0])
                height = int(numbers[1])
                return width, height
            
            # Default fallback
            logger.warning(f"Could not parse screen size from: {output}, using defaults")
            return 1080, 1920
            
        except Exception as e:
            logger.warning(f"Failed to parse screen size from '{output}': {e}, using defaults")
            return 1080, 1920
    
    def _parse_density(self, output: str) -> int:
        """Parse density from wm density output with robust handling."""
        try:
            # Clean the output
            output = output.strip()
            
            # Handle different formats:
            # Format 1: "Physical density: 420"
            # Format 2: Just "420"
            
            if ": " in output:
                # Standard format
                density_part = output.split(": ")[1].strip()
            else:
                # Direct number or first line
                lines = output.split('\n')
                density_part = lines[0].strip()
            
            # Extract first number from the string
            import re
            numbers = re.findall(r'\d+', density_part)
            if numbers:
                return int(numbers[0])
            
            # Default fallback
            logger.warning(f"Could not parse density from: {output}, using default")
            return 420
            
        except Exception as e:
            logger.warning(f"Failed to parse density from '{output}': {e}, using default")
            return 420
