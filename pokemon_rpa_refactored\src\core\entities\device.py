"""Device entity representing an Android device for automation."""

from dataclasses import dataclass, field
from typing import Optional
from enum import Enum
from datetime import datetime


class DeviceStatus(Enum):
    """Device connection status."""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    UNAUTHORIZED = "unauthorized"
    OFFLINE = "offline"


class DeviceType(Enum):
    """Device type classification."""
    ANDROID = "android"
    PHONE = "phone"
    TABLET = "tablet"
    EMULATOR = "emulator"


@dataclass(frozen=True)
class DeviceInfo:
    """Device information and specifications."""
    device_id: str
    model: str
    manufacturer: str
    brand: str
    product: str
    android_version: str
    screen_width: int
    screen_height: int
    density: int
    device_type: DeviceType
    api_level: int = 0  # Android API level


@dataclass
class Device:
    """Device entity for automation."""
    
    device_id: str
    status: DeviceStatus
    device_type: DeviceType = DeviceType.ANDROID
    name: str = ""
    model: str = ""
    android_version: str = ""
    info: Optional[DeviceInfo] = None
    last_screenshot_path: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """Validate device after initialization."""
        if not self.device_id:
            raise ValueError("Device ID cannot be empty")
    
    @property
    def is_connected(self) -> bool:
        """Check if device is connected and ready."""
        return self.status == DeviceStatus.CONNECTED
    
    @property
    def screen_resolution(self) -> tuple[int, int]:
        """Get screen resolution as (width, height)."""
        if not self.info:
            raise ValueError("Device info not available")
        return (self.info.screen_width, self.info.screen_height)
    
    def update_status(self, status: DeviceStatus) -> None:
        """Update device status."""
        self.status = status
    
    def update_info(self, info: DeviceInfo) -> None:
        """Update device information."""
        if info.device_id != self.device_id:
            raise ValueError("Device ID mismatch")
        self.info = info
