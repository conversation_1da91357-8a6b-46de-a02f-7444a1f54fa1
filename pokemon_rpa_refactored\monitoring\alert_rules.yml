# Alert rules for Pokemon RPA Framework
groups:
  - name: pokemon_rpa_alerts
    rules:
      # Application Health Alerts
      - alert: PokemonRPADown
        expr: up{job="pokemon-rpa"} == 0
        for: 1m
        labels:
          severity: critical
          service: pokemon-rpa
        annotations:
          summary: "Pokemon RPA application is down"
          description: "Pokemon RPA application has been down for more than 1 minute."

      - alert: PokemonRPAHighErrorRate
        expr: rate(pokemon_rpa_http_requests_total{status=~"5.."}[5m]) / rate(pokemon_rpa_http_requests_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "High error rate in Pokemon RPA"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes."

      - alert: PokemonRPAHighResponseTime
        expr: histogram_quantile(0.95, rate(pokemon_rpa_http_request_duration_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "High response time in Pokemon RPA"
          description: "95th percentile response time is {{ $value }}s for the last 5 minutes."

      # Automation Session Alerts
      - alert: HighFailedAutomationSessions
        expr: rate(pokemon_rpa_automation_sessions_total{status="failed"}[10m]) > 0.2
        for: 5m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "High rate of failed automation sessions"
          description: "Failed automation session rate is {{ $value }} per second over the last 10 minutes."

      - alert: LongRunningAutomationSession
        expr: pokemon_rpa_automation_session_duration_seconds > 3600
        for: 0m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "Long running automation session detected"
          description: "Automation session {{ $labels.session_id }} has been running for {{ $value | humanizeDuration }}."

      - alert: NoActiveAutomationSessions
        expr: pokemon_rpa_active_automation_sessions == 0
        for: 30m
        labels:
          severity: info
          service: pokemon-rpa
        annotations:
          summary: "No active automation sessions"
          description: "There have been no active automation sessions for 30 minutes."

      # Device Connection Alerts
      - alert: DeviceDisconnected
        expr: pokemon_rpa_connected_devices == 0
        for: 5m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "No devices connected"
          description: "No devices have been connected for the last 5 minutes."

      - alert: DeviceConnectionFailure
        expr: rate(pokemon_rpa_device_connection_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "High device connection failure rate"
          description: "Device connection failure rate is {{ $value }} per second."

      # Resource Usage Alerts
      - alert: HighCPUUsage
        expr: pokemon_rpa_cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% for the last 5 minutes."

      - alert: HighMemoryUsage
        expr: pokemon_rpa_memory_usage_percent > 85
        for: 5m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% for the last 5 minutes."

      - alert: LowDiskSpace
        expr: pokemon_rpa_disk_usage_percent > 90
        for: 2m
        labels:
          severity: critical
          service: pokemon-rpa
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value }}%."

  - name: database_alerts
    rules:
      # MongoDB Alerts
      - alert: MongoDBDown
        expr: up{job="mongodb"} == 0
        for: 1m
        labels:
          severity: critical
          service: mongodb
        annotations:
          summary: "MongoDB is down"
          description: "MongoDB has been down for more than 1 minute."

      - alert: MongoDBHighConnections
        expr: mongodb_connections{state="current"} / mongodb_connections{state="available"} > 0.8
        for: 5m
        labels:
          severity: warning
          service: mongodb
        annotations:
          summary: "MongoDB high connection usage"
          description: "MongoDB connection usage is {{ $value | humanizePercentage }}."

      - alert: MongoDBSlowQueries
        expr: rate(mongodb_op_counters_total{type="query"}[5m]) > 1000
        for: 3m
        labels:
          severity: warning
          service: mongodb
        annotations:
          summary: "MongoDB high query rate"
          description: "MongoDB query rate is {{ $value }} queries per second."

      - alert: MongoDBReplicationLag
        expr: mongodb_replset_member_replication_lag > 10
        for: 2m
        labels:
          severity: warning
          service: mongodb
        annotations:
          summary: "MongoDB replication lag"
          description: "MongoDB replication lag is {{ $value }} seconds."

  - name: infrastructure_alerts
    rules:
      # Container/System Alerts
      - alert: ContainerHighCPU
        expr: rate(container_cpu_usage_seconds_total{name!=""}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: container
        annotations:
          summary: "Container high CPU usage"
          description: "Container {{ $labels.name }} CPU usage is {{ $value }}%."

      - alert: ContainerHighMemory
        expr: container_memory_usage_bytes{name!=""} / container_spec_memory_limit_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: container
        annotations:
          summary: "Container high memory usage"
          description: "Container {{ $labels.name }} memory usage is {{ $value }}%."

      - alert: ContainerRestarting
        expr: rate(container_last_seen[5m]) > 0
        for: 1m
        labels:
          severity: warning
          service: container
        annotations:
          summary: "Container restarting"
          description: "Container {{ $labels.name }} is restarting frequently."

      # Network Alerts
      - alert: HighNetworkErrors
        expr: rate(node_network_receive_errs_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          service: network
        annotations:
          summary: "High network errors"
          description: "Network interface {{ $labels.device }} has {{ $value }} errors per second."

  - name: business_logic_alerts
    rules:
      # OCR Performance Alerts
      - alert: OCRHighLatency
        expr: histogram_quantile(0.95, rate(pokemon_rpa_ocr_duration_seconds_bucket[5m])) > 5
        for: 3m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "OCR high latency"
          description: "95th percentile OCR processing time is {{ $value }}s."

      - alert: OCRHighFailureRate
        expr: rate(pokemon_rpa_ocr_failures_total[5m]) / rate(pokemon_rpa_ocr_requests_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "OCR high failure rate"
          description: "OCR failure rate is {{ $value | humanizePercentage }}."

      # Image Detection Alerts
      - alert: ImageDetectionHighLatency
        expr: histogram_quantile(0.95, rate(pokemon_rpa_image_detection_duration_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "Image detection high latency"
          description: "95th percentile image detection time is {{ $value }}s."

      # Notification Alerts
      - alert: NotificationFailures
        expr: rate(pokemon_rpa_notification_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "High notification failure rate"
          description: "Notification failure rate is {{ $value }} per second."

  - name: security_alerts
    rules:
      # Security-related alerts
      - alert: UnauthorizedAccess
        expr: rate(pokemon_rpa_http_requests_total{status="401"}[5m]) > 0.1
        for: 1m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "High rate of unauthorized access attempts"
          description: "Unauthorized access attempt rate is {{ $value }} per second."

      - alert: SuspiciousActivity
        expr: rate(pokemon_rpa_http_requests_total{status="403"}[5m]) > 0.05
        for: 1m
        labels:
          severity: warning
          service: pokemon-rpa
        annotations:
          summary: "Suspicious activity detected"
          description: "Forbidden request rate is {{ $value }} per second."
