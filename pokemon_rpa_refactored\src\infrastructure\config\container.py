"""Dependency injection container configuration."""

from dependency_injector import containers, providers

from .settings import get_settings
from ..adb.adb_device_controller import ADBDeviceController
from ..vision.opencv_image_detector import OpenCVImageDetector
from ..ocr.tesseract_ocr_reader import Tess<PERSON>ct<PERSON><PERSON><PERSON>eader
from ..database.mongo_repository import MongoRepository
from ..external.telegram_notifier import TelegramNotifier
from ...core.use_cases.adventure_automation import AdventureAutomationUseCase
from ...application.services.automation_service import AutomationService
from ...application.services.device_service import DeviceService


class Container(containers.DeclarativeContainer):
    """Dependency injection container."""
    
    # Configuration
    config = providers.Configuration()
    
    # Infrastructure - Device Control
    adb_device_controller = providers.Singleton(
        ADBDeviceController,
        host=config.adb.host,
        port=config.adb.port
    )
    
    # Infrastructure - Computer Vision
    opencv_image_detector = providers.Singleton(
        OpenCVImageDetector,
        templates_path=config.computer_vision.templates_path
    )
    
    # Infrastructure - OCR
    tesseract_ocr_reader = providers.Singleton(
        TesseractOCRReader,
        tesseract_path=config.computer_vision.tesseract_path
    )
    
    # Infrastructure - Database
    database_repository = providers.Singleton(
        MongoRepository,
        connection_string=config.database.connection_string,
        database_name=config.database.database_name
    )
    
    # Infrastructure - External Services
    telegram_notifier = providers.Singleton(
        TelegramNotifier,
        bot_token=config.telegram.bot_token,
        chat_id=config.telegram.chat_id
    )
    
    # Core - Use Cases
    adventure_automation_use_case = providers.Factory(
        AdventureAutomationUseCase,
        device_controller=adb_device_controller,
        image_detector=opencv_image_detector,
        ocr_reader=tesseract_ocr_reader
    )
    
    # Application - Services
    device_service = providers.Factory(
        DeviceService,
        device_controller=adb_device_controller,
        repository=database_repository
    )
    
    automation_service = providers.Factory(
        AutomationService,
        adventure_use_case=adventure_automation_use_case,
        device_service=device_service,
        notifier=telegram_notifier,
        repository=database_repository
    )


def configure_container() -> Container:
    """Configure and return the dependency injection container."""
    
    container = Container()
    
    # Load settings
    settings = get_settings()
    
    # Configure container with settings
    container.config.from_dict({
        "adb": {
            "host": settings.device.adb.host,
            "port": settings.device.adb.port
        },
        "computer_vision": {
            "templates_path": "./assets/templates",
            "tesseract_path": settings.computer_vision.tesseract_path
        },
        "database": {
            "connection_string": settings.database.connection_string,
            "database_name": settings.database.database
        },
        "telegram": {
            "bot_token": settings.services.telegram.bot_token,
            "chat_id": settings.services.telegram.chat_id
        }
    })
    
    # Wire dependencies
    container.wire(modules=[
        "src.core.use_cases.adventure_automation",
        "src.application.services.automation_service",
        "src.application.services.device_service",
        "src.presentation.cli.main"
    ])
    
    return container
