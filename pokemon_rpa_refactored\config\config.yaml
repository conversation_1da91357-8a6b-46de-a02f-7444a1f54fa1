# Pokemon RPA Configuration
app:
  name: "Pokemon RPA Framework"
  version: "1.0.0"
  environment: "development"  # development, staging, production
  debug: true

# Device configuration
device:
  adb:
    host: "localhost"
    port: 5037
    timeout: 30
  screenshot:
    format: "PNG"
    quality: 90
    temp_dir: "./temp"

# Computer Vision settings
computer_vision:
  template_matching:
    threshold: 0.8
    method: "TM_CCOEFF_NORMED"
  ocr:
    tesseract_path: "C:/Program Files (x86)/Tesseract-OCR/tesseract.exe"
    language: "eng"
    config: "--psm 8"

# Database configuration
database:
  mongodb:
    host: "localhost"
    port: 27017
    database: "pokemon_rpa"
    collection: "users"
    connection_timeout: 5000

# External services
services:
  telegram:
    enabled: false
    bot_token: "${TELEGRAM_BOT_TOKEN}"
    chat_id: "${TELEGRAM_CHAT_ID}"
  
  vpn:
    enabled: false
    provider: "nordvpn"
    auto_reconnect: true

# Automation settings
automation:
  delays:
    click: 0.5
    screenshot: 1.0
    action: 2.0
  retries:
    max_attempts: 3
    backoff_factor: 2
  
  adventure:
    max_duration: 3600  # 1 hour
    check_interval: 30   # 30 seconds

# Logging configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "json"  # json, text
  file:
    enabled: true
    path: "./logs/app.log"
    max_size: "10MB"
    backup_count: 5
  console:
    enabled: true
    colorize: true

# Monitoring
monitoring:
  metrics:
    enabled: true
    port: 8000
    path: "/metrics"
  health_check:
    enabled: true
    port: 8001
    path: "/health"
