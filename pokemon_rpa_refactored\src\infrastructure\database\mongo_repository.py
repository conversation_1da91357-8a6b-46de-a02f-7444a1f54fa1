"""MongoDB implementation of repository interface."""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import structlog
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import PyMongoError

from ...core.interfaces.repository import IRepository
from ...core.entities.device import Device, DeviceStatus, DeviceType, DeviceInfo
from ...core.entities.automation import AutomationSession, AutomationStatus, ActionType, AutomationAction


logger = structlog.get_logger(__name__)


class MongoRepository(IRepository):
    """MongoDB implementation of repository interface."""
    
    def __init__(self, connection_string: str, database_name: str = "pokemon_rpa"):
        self.connection_string = connection_string
        self.database_name = database_name
        self._client: Optional[AsyncIOMotorClient] = None
        self._db: Optional[AsyncIOMotorDatabase] = None
    
    async def _get_database(self) -> AsyncIOMotorDatabase:
        """Get or create database connection."""
        if self._client is None:
            self._client = AsyncIOMotorClient(self.connection_string)
            self._db = self._client[self.database_name]
            
            # Create indexes
            await self._create_indexes()
        
        return self._db
    
    async def _create_indexes(self) -> None:
        """Create database indexes for better performance."""
        try:
            db = self._db
            
            # Device indexes
            await db.devices.create_index("device_id", unique=True)
            await db.devices.create_index("status")
            
            # Session indexes
            await db.automation_sessions.create_index("session_id", unique=True)
            await db.automation_sessions.create_index("user_name")
            await db.automation_sessions.create_index("device_id")
            await db.automation_sessions.create_index("status")
            await db.automation_sessions.create_index("started_at")
            await db.automation_sessions.create_index([("user_name", 1), ("started_at", -1)])
            
            logger.debug("Database indexes created")
            
        except Exception as e:
            logger.error("Failed to create database indexes", error=str(e))
    
    # Device operations
    async def save_device(self, device: Device) -> bool:
        """Save device information."""
        try:
            db = await self._get_database()
            
            device_doc = {
                "device_id": device.device_id,
                "status": device.status.value,
                "device_type": device.device_type.value,
                "info": {
                    "model": device.info.model if device.info else None,
                    "android_version": device.info.android_version if device.info else None,
                    "api_level": device.info.api_level if device.info else None,
                    "manufacturer": device.info.manufacturer if device.info else None,
                    "brand": device.info.brand if device.info else None,
                    "product": device.info.product if device.info else None,
                } if device.info else None,
                "screen_resolution": device.screen_resolution,
                "last_seen": datetime.utcnow(),
                "created_at": device.created_at or datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            await db.devices.replace_one(
                {"device_id": device.device_id},
                device_doc,
                upsert=True
            )
            
            logger.debug("Device saved", device_id=device.device_id)
            return True
            
        except Exception as e:
            logger.error("Failed to save device", device_id=device.device_id, error=str(e))
            return False
    
    async def get_device(self, device_id: str) -> Optional[Device]:
        """Get device by ID."""
        try:
            db = await self._get_database()
            
            doc = await db.devices.find_one({"device_id": device_id})
            if not doc:
                return None
            
            return self._document_to_device(doc)
            
        except Exception as e:
            logger.error("Failed to get device", device_id=device_id, error=str(e))
            return None
    
    async def list_devices(self) -> List[Device]:
        """List all devices."""
        try:
            db = await self._get_database()
            
            devices = []
            async for doc in db.devices.find():
                device = self._document_to_device(doc)
                if device:
                    devices.append(device)
            
            logger.debug("Listed devices", count=len(devices))
            return devices
            
        except Exception as e:
            logger.error("Failed to list devices", error=str(e))
            return []
    
    async def delete_device(self, device_id: str) -> bool:
        """Delete device by ID."""
        try:
            db = await self._get_database()
            
            result = await db.devices.delete_one({"device_id": device_id})
            
            if result.deleted_count > 0:
                logger.debug("Device deleted", device_id=device_id)
                return True
            else:
                logger.warning("Device not found for deletion", device_id=device_id)
                return False
                
        except Exception as e:
            logger.error("Failed to delete device", device_id=device_id, error=str(e))
            return False
    
    # Automation session operations
    async def save_automation_session(self, session: AutomationSession) -> bool:
        """Save automation session."""
        try:
            db = await self._get_database()
            
            session_doc = {
                "session_id": session.session_id,
                "user_name": session.user_name,
                "device_id": session.device_id,
                "session_type": session.session_type,
                "status": session.status.value,
                "started_at": session.started_at,
                "completed_at": session.completed_at,
                "error_message": session.error_message,
                "actions": [
                    {
                        "action_type": action.action_type.value,
                        "parameters": action.parameters,
                        "timestamp": action.timestamp,
                        "duration_ms": action.duration_ms,
                        "is_successful": action.is_successful,
                        "error": action.error
                    }
                    for action in session.actions
                ],
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            await db.automation_sessions.replace_one(
                {"session_id": session.session_id},
                session_doc,
                upsert=True
            )
            
            logger.debug("Automation session saved", session_id=session.session_id)
            return True
            
        except Exception as e:
            logger.error("Failed to save automation session", session_id=session.session_id, error=str(e))
            return False
    
    async def get_automation_session(self, session_id: str) -> Optional[AutomationSession]:
        """Get automation session by ID."""
        try:
            db = await self._get_database()
            
            doc = await db.automation_sessions.find_one({"session_id": session_id})
            if not doc:
                return None
            
            return self._document_to_session(doc)
            
        except Exception as e:
            logger.error("Failed to get automation session", session_id=session_id, error=str(e))
            return None
    
    async def update_automation_session(self, session: AutomationSession) -> bool:
        """Update automation session."""
        return await self.save_automation_session(session)
    
    async def list_automation_sessions(self, limit: int = 100, offset: int = 0) -> List[AutomationSession]:
        """List automation sessions with pagination."""
        try:
            db = await self._get_database()
            
            sessions = []
            async for doc in db.automation_sessions.find().sort("started_at", -1).skip(offset).limit(limit):
                session = self._document_to_session(doc)
                if session:
                    sessions.append(session)
            
            logger.debug("Listed automation sessions", count=len(sessions))
            return sessions
            
        except Exception as e:
            logger.error("Failed to list automation sessions", error=str(e))
            return []
    
    async def get_user_sessions(self, user_name: str, limit: int = 10) -> List[AutomationSession]:
        """Get automation sessions for a specific user."""
        try:
            db = await self._get_database()
            
            sessions = []
            async for doc in db.automation_sessions.find({"user_name": user_name}).sort("started_at", -1).limit(limit):
                session = self._document_to_session(doc)
                if session:
                    sessions.append(session)
            
            logger.debug("Listed user sessions", user_name=user_name, count=len(sessions))
            return sessions
            
        except Exception as e:
            logger.error("Failed to get user sessions", user_name=user_name, error=str(e))
            return []
    
    async def delete_automation_session(self, session_id: str) -> bool:
        """Delete automation session by ID."""
        try:
            db = await self._get_database()
            
            result = await db.automation_sessions.delete_one({"session_id": session_id})
            
            if result.deleted_count > 0:
                logger.debug("Automation session deleted", session_id=session_id)
                return True
            else:
                logger.warning("Automation session not found for deletion", session_id=session_id)
                return False
                
        except Exception as e:
            logger.error("Failed to delete automation session", session_id=session_id, error=str(e))
            return False
    
    async def delete_old_sessions(self, days_old: int = 7) -> int:
        """Delete automation sessions older than specified days."""
        try:
            db = await self._get_database()
            
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            result = await db.automation_sessions.delete_many({
                "started_at": {"$lt": cutoff_date}
            })
            
            deleted_count = result.deleted_count
            logger.info("Deleted old automation sessions", count=deleted_count, days_old=days_old)
            return deleted_count
            
        except Exception as e:
            logger.error("Failed to delete old sessions", days_old=days_old, error=str(e))
            return 0
    
    async def get_sessions_by_status(self, status: str, limit: int = 100) -> List[AutomationSession]:
        """Get automation sessions by status."""
        try:
            db = await self._get_database()
            
            sessions = []
            async for doc in db.automation_sessions.find({"status": status}).sort("started_at", -1).limit(limit):
                session = self._document_to_session(doc)
                if session:
                    sessions.append(session)
            
            logger.debug("Listed sessions by status", status=status, count=len(sessions))
            return sessions
            
        except Exception as e:
            logger.error("Failed to get sessions by status", status=status, error=str(e))
            return []
    
    async def get_sessions_by_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
        limit: int = 100
    ) -> List[AutomationSession]:
        """Get automation sessions within date range."""
        try:
            db = await self._get_database()
            
            sessions = []
            async for doc in db.automation_sessions.find({
                "started_at": {
                    "$gte": start_date,
                    "$lte": end_date
                }
            }).sort("started_at", -1).limit(limit):
                session = self._document_to_session(doc)
                if session:
                    sessions.append(session)
            
            logger.debug("Listed sessions by date range", count=len(sessions))
            return sessions
            
        except Exception as e:
            logger.error("Failed to get sessions by date range", error=str(e))
            return []
    
    async def health_check(self) -> dict:
        """Perform health check of repository."""
        try:
            db = await self._get_database()
            
            # Test connection
            await db.command("ping")
            
            # Get collection stats
            device_count = await db.devices.count_documents({})
            session_count = await db.automation_sessions.count_documents({})
            
            return {
                "healthy": True,
                "database_name": self.database_name,
                "device_count": device_count,
                "session_count": session_count,
                "connection_ok": True
            }
            
        except Exception as e:
            logger.error("Repository health check failed", error=str(e))
            return {
                "healthy": False,
                "error": str(e),
                "connection_ok": False
            }
    
    def _document_to_device(self, doc: Dict[str, Any]) -> Optional[Device]:
        """Convert MongoDB document to Device entity."""
        try:
            info = None
            if doc.get("info"):
                info_data = doc["info"]
                # Get screen resolution from device document
                screen_resolution = doc.get("screen_resolution", (1080, 1920))
                
                info = DeviceInfo(
                    device_id=doc["device_id"],
                    model=info_data.get("model", "Unknown"),
                    manufacturer=info_data.get("manufacturer", "Unknown"),
                    brand=info_data.get("brand", "Unknown"),
                    product=info_data.get("product", "Unknown"),
                    android_version=info_data.get("android_version", "Unknown"),
                    screen_width=screen_resolution[0] if isinstance(screen_resolution, (list, tuple)) else 1080,
                    screen_height=screen_resolution[1] if isinstance(screen_resolution, (list, tuple)) else 1920,
                    density=info_data.get("density", 420),
                    device_type=DeviceType(doc.get("device_type", "android")),
                    api_level=info_data.get("api_level", 0)
                )
            
            return Device(
                device_id=doc["device_id"],
                status=DeviceStatus(doc["status"]),
                device_type=DeviceType(doc.get("device_type", "android")),
                info=info,
                last_screenshot_path=doc.get("last_screenshot_path"),
                created_at=doc.get("created_at")
            )
            
        except Exception as e:
            logger.error("Failed to convert document to device", error=str(e))
            return None
    
    def _document_to_session(self, doc: Dict[str, Any]) -> Optional[AutomationSession]:
        """Convert MongoDB document to AutomationSession entity."""
        try:
            actions = []
            for action_doc in doc.get("actions", []):
                action = AutomationAction(
                    action_type=ActionType(action_doc["action_type"]),
                    parameters=action_doc["parameters"],
                    timestamp=action_doc["timestamp"],
                    duration_ms=action_doc.get("duration_ms"),
                    is_successful=action_doc.get("is_successful", False),
                    error=action_doc.get("error")
                )
                actions.append(action)
            
            return AutomationSession(
                session_id=doc["session_id"],
                user_name=doc["user_name"],
                device_id=doc["device_id"],
                session_type=doc["session_type"],
                status=AutomationStatus(doc["status"]),
                started_at=doc["started_at"],
                completed_at=doc.get("completed_at"),
                error_message=doc.get("error_message"),
                actions=actions
            )
            
        except Exception as e:
            logger.error("Failed to convert document to session", error=str(e))
            return None
    
    async def close(self) -> None:
        """Close database connection."""
        if self._client:
            self._client.close()
            self._client = None
            self._db = None
            logger.debug("Database connection closed")
