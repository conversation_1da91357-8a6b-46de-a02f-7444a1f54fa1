"""Command-line interface for Pokemon RPA Framework."""

import asyncio
import sys
from typing import Optional
import click
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from dependency_injector.wiring import Provide, inject
import functools

from ...application.services.automation_service import AutomationService
from ...application.services.device_service import DeviceService
from ...infrastructure.config.container import configure_container
from ...infrastructure.config.settings import get_settings


console = Console()


def async_command(f):
    """Decorator to make async commands work with Click."""
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper


@click.group()
@click.version_option(version="1.0.0", prog_name="Pokemon RPA Framework")
def cli():
    """Pokemon GO RPA Automation Framework - Professional Edition"""
    pass


@cli.command()
@click.option("--device-id", "-d", help="Device ID to connect to")
@click.option("--user", "-u", required=True, help="Username for automation")
@click.option("--duration", "-t", default=3600, help="Max duration in seconds")
@async_command
@inject
async def adventure(
    device_id: Optional[str],
    user: str,
    duration: int,
    automation_service: AutomationService = Provide["automation_service"]
):
    """Start adventure automation for a user."""
    
    settings = get_settings()
    if not device_id:
        device_id = settings.device.default_device_id
        if not device_id:
            print("Error: No device ID provided and no default configured")
            sys.exit(1)
    
    print(f"Starting adventure automation for user: {user}")
    print(f"Device ID: {device_id}")
    print(f"Max duration: {duration} seconds")
    
    try:
        print("Starting automation...")
        
        session_id = await automation_service.start_adventure_automation(
            device_id=device_id,
            user_name=user,
            max_duration_seconds=duration
        )
        
        print("Automation completed!")
        print(f"Automation completed successfully!")
        print(f"Session ID: {session_id}")
        
        # Show session statistics
        try:
            stats = await automation_service.get_session_statistics(session_id)
            print("\nSession Statistics:")
            print(f"- Duration: {stats.get('duration', 'N/A')} seconds")
            print(f"- Actions performed: {stats.get('actions_count', 'N/A')}")
            print(f"- Status: {stats.get('status', 'N/A')}")
        except Exception as stats_error:
            print(f"Could not retrieve session statistics: {stats_error}")
        
    except Exception as e:
        print(f"Automation failed: {str(e)}")
        sys.exit(1)


@cli.command()
@click.option("--session-id", "-s", required=True, help="Session ID to check")
@async_command
@inject
async def status(
    session_id: str,
    automation_service: AutomationService = Provide["automation_service"]
):
    """Check status of an automation session."""
    
    try:
        session = await automation_service.get_session_status(session_id)
        
        if not session:
            console.print(f"[red]Session {session_id} not found[/red]")
            sys.exit(1)
        
        console.print(f"[green]Session Status: {session.status.value}[/green]")
        
        if session.status.value in ["completed", "failed"]:
            stats = await automation_service.get_session_statistics(session_id)
            display_session_stats(stats)
        
    except Exception as e:
        console.print(f"[red]Error checking status: {str(e)}[/red]")
        sys.exit(1)


@cli.command()
@async_command
@inject
async def list_sessions(
    automation_service: AutomationService = Provide["automation_service"]
):
    """List all active automation sessions."""
    
    try:
        sessions = await automation_service.list_active_sessions()
        
        if not sessions:
            console.print("[yellow]No active sessions found[/yellow]")
            return
        
        table = Table(title="Active Automation Sessions")
        table.add_column("Session ID", style="cyan")
        table.add_column("User", style="green")
        table.add_column("Device ID", style="blue")
        table.add_column("Status", style="yellow")
        table.add_column("Started", style="magenta")
        
        for session in sessions:
            table.add_row(
                session.session_id[:8] + "...",
                session.user_name,
                session.device_id,
                session.status.value,
                session.started_at.strftime("%H:%M:%S") if session.started_at else "N/A"
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]Error listing sessions: {str(e)}[/red]")
        sys.exit(1)


@cli.command()
@click.option("--device-id", "-d", help="Device ID to connect to")
@async_command
@inject
async def devices(
    device_id: Optional[str],
    device_service: DeviceService = Provide["device_service"]
):
    """List or connect to devices."""
    
    try:
        if device_id:
            # Connect to specific device
            print(f"Connecting to device: {device_id}")
            device = await device_service.connect_device(device_id)
            print(f"Connected to {device.info.model}")
            print(f"Android version: {device.info.android_version}")
            print(f"Screen resolution: {device.screen_resolution}")
        else:
            # List available devices
            devices = await device_service.list_available_devices()
            
            if not devices:
                print("No devices found")
                return
            
            print("\nAvailable Devices:")
            print("-" * 80)
            print(f"{'Device ID':<25} {'Status':<15} {'Model':<20} {'Android Version':<15}")
            print("-" * 80)
            
            for device in devices:
                device_id = device.device_id
                status = device.status.value
                model = device.info.model if device.info else "Unknown"
                android_version = device.info.android_version if device.info else "Unknown"
                
                print(f"{device_id:<25} {status:<15} {model:<20} {android_version:<15}")
            
            print("-" * 80)
    
    except Exception as e:
        print(f"Error with devices: {str(e)}")
        sys.exit(1)


@cli.command()
@async_command
@inject
async def health(
    automation_service: AutomationService = Provide["automation_service"]
):
    """Check system health status."""
    
    try:
        # Simple text-based progress without Rich Progress
        print("Checking system health...")
        
        health_status = await automation_service.health_check()
        
        print("Health check completed!")
        
        if health_status["healthy"]:
            print(" System is healthy")
        else:
            print(" System has issues")
        
        # Display component status in simple format
        print("\nComponent Health Status:")
        print("-" * 50)
        
        for component, status in health_status.get("components", {}).items():
            status_text = "Healthy" if status.get("healthy", False) else "Unhealthy"
            details = status.get("error", "OK") if not status.get("healthy", False) else "OK"
            print(f"{component:20} | {status_text:10} | {details}")
        
        print("-" * 50)
        print(f"Active sessions: {health_status.get('active_sessions', 0)}")
        print(f"Connected devices: {health_status.get('connected_devices', 0)}")
        print(f"Available devices: {health_status.get('available_devices', 0)}")
        print(f"ADB available: {health_status.get('adb_available', False)}")
        
    except Exception as e:
        print(f"Error checking health: {str(e)}")
        sys.exit(1)


@cli.command()
@click.option("--days", "-d", default=7, help="Delete sessions older than X days")
@async_command
@inject
async def cleanup(
    days: int,
    automation_service: AutomationService = Provide["automation_service"]
):
    """Clean up old automation sessions."""
    
    try:
        console.print(f"[blue]Cleaning up sessions older than {days} days...[/blue]")
        
        deleted_count = await automation_service.cleanup_old_sessions(days)
        
        console.print(f"[green]✓ Cleaned up {deleted_count} old sessions[/green]")
        
    except Exception as e:
        console.print(f"[red]Error during cleanup: {str(e)}[/red]")
        sys.exit(1)


def display_session_stats(stats: dict) -> None:
    """Display session statistics in a formatted table."""
    
    table = Table(title="Session Statistics")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Session ID", stats["session_id"][:16] + "...")
    table.add_row("Status", stats["status"])
    table.add_row("User", stats["user_name"])
    table.add_row("Device", stats["device_id"])
    table.add_row("Duration", f"{stats['duration_seconds']:.1f}s" if stats["duration_seconds"] else "N/A")
    table.add_row("Total Actions", str(stats["total_actions"]))
    table.add_row("Successful Actions", str(stats["successful_actions"]))
    table.add_row("Failed Actions", str(stats["failed_actions"]))
    table.add_row("Success Rate", f"{stats['success_rate']:.1%}")
    table.add_row("Avg Action Duration", f"{stats['average_action_duration_ms']:.1f}ms")
    
    console.print(table)


def main():
    """Main entry point."""
    # Configure dependency injection
    container = configure_container()
    
    # Wire the CLI module
    container.wire(modules=[__name__])
    
    try:
        cli()
    finally:
        # Unwire when done
        container.unwire()


if __name__ == "__main__":
    main()
