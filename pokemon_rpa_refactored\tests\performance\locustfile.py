"""Locust load testing configuration for Pokemon RPA Framework."""

import asyncio
import json
import random
import time
from locust import HttpUser, task, between, events
from locust.exception import StopUser


class PokemonRPAUser(HttpUser):
    """Simulated user for load testing the Pokemon RPA Framework API."""
    
    wait_time = between(1, 3)  # Wait 1-3 seconds between tasks
    
    def on_start(self):
        """Initialize user session."""
        self.user_id = f"load_test_user_{random.randint(1000, 9999)}"
        self.device_id = f"load_test_device_{random.randint(1000, 9999)}"
        self.session_id = None
        self.auth_token = None
        
        # Authenticate user (if authentication is implemented)
        self.authenticate()
    
    def authenticate(self):
        """Authenticate the user."""
        # Mock authentication - replace with actual auth endpoint
        auth_data = {
            "username": self.user_id,
            "password": "test_password"
        }
        
        # Uncomment when auth endpoint is available
        # response = self.client.post("/auth/login", json=auth_data)
        # if response.status_code == 200:
        #     self.auth_token = response.json().get("token")
        
        # For now, use mock token
        self.auth_token = "mock_auth_token"
    
    def get_headers(self):
        """Get headers with authentication."""
        headers = {"Content-Type": "application/json"}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers
    
    @task(3)
    def check_system_health(self):
        """Check system health endpoint."""
        with self.client.get(
            "/health", 
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                health_data = response.json()
                if health_data.get("healthy", False):
                    response.success()
                else:
                    response.failure("System reported as unhealthy")
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(2)
    def list_devices(self):
        """List available devices."""
        with self.client.get(
            "/api/devices", 
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                devices = response.json()
                if isinstance(devices, list):
                    response.success()
                else:
                    response.failure("Invalid devices response format")
            else:
                response.failure(f"List devices failed: {response.status_code}")
    
    @task(1)
    def register_device(self):
        """Register a new device."""
        device_data = {
            "device_id": self.device_id,
            "device_type": "android",
            "screen_resolution": [1080, 1920],
            "android_version": "11",
            "model": "Test Device"
        }
        
        with self.client.post(
            "/api/devices", 
            json=device_data,
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code in [200, 201]:
                response.success()
            elif response.status_code == 409:  # Device already exists
                response.success()
            else:
                response.failure(f"Device registration failed: {response.status_code}")
    
    @task(4)
    def start_automation(self):
        """Start an automation session."""
        if self.session_id:
            # Already have an active session
            return
        
        automation_data = {
            "device_id": self.device_id,
            "user_name": self.user_id,
            "automation_type": "adventure",
            "settings": {
                "max_duration": 300,  # 5 minutes
                "auto_stop": True
            }
        }
        
        with self.client.post(
            "/api/automation/start", 
            json=automation_data,
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code in [200, 201]:
                result = response.json()
                self.session_id = result.get("session_id")
                if self.session_id:
                    response.success()
                else:
                    response.failure("No session ID returned")
            else:
                response.failure(f"Start automation failed: {response.status_code}")
    
    @task(2)
    def check_session_status(self):
        """Check automation session status."""
        if not self.session_id:
            return
        
        with self.client.get(
            f"/api/automation/sessions/{self.session_id}", 
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                session_data = response.json()
                status = session_data.get("status")
                if status in ["running", "completed", "failed", "stopped"]:
                    response.success()
                    
                    # If session is completed or failed, clear session_id
                    if status in ["completed", "failed", "stopped"]:
                        self.session_id = None
                else:
                    response.failure(f"Invalid session status: {status}")
            elif response.status_code == 404:
                # Session not found, clear session_id
                self.session_id = None
                response.success()
            else:
                response.failure(f"Session status check failed: {response.status_code}")
    
    @task(1)
    def stop_automation(self):
        """Stop the automation session."""
        if not self.session_id:
            return
        
        with self.client.post(
            f"/api/automation/sessions/{self.session_id}/stop", 
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                self.session_id = None
                response.success()
            elif response.status_code == 404:
                # Session not found
                self.session_id = None
                response.success()
            else:
                response.failure(f"Stop automation failed: {response.status_code}")
    
    @task(1)
    def list_user_sessions(self):
        """List user's automation sessions."""
        with self.client.get(
            f"/api/automation/sessions?user={self.user_id}", 
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                sessions = response.json()
                if isinstance(sessions, list):
                    response.success()
                else:
                    response.failure("Invalid sessions response format")
            else:
                response.failure(f"List sessions failed: {response.status_code}")
    
    @task(1)
    def get_automation_statistics(self):
        """Get automation statistics."""
        with self.client.get(
            "/api/automation/statistics", 
            headers=self.get_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                stats = response.json()
                required_fields = ["total_sessions", "successful_sessions", "failed_sessions"]
                if all(field in stats for field in required_fields):
                    response.success()
                else:
                    response.failure("Missing required statistics fields")
            else:
                response.failure(f"Get statistics failed: {response.status_code}")
    
    def on_stop(self):
        """Clean up when user stops."""
        # Stop any active automation session
        if self.session_id:
            try:
                self.client.post(
                    f"/api/automation/sessions/{self.session_id}/stop",
                    headers=self.get_headers()
                )
            except:
                pass  # Ignore errors during cleanup


class AdminUser(HttpUser):
    """Admin user for testing administrative endpoints."""
    
    wait_time = between(5, 10)  # Longer wait time for admin tasks
    weight = 1  # Lower weight (fewer admin users)
    
    def on_start(self):
        """Initialize admin session."""
        self.admin_token = "admin_mock_token"  # Replace with actual admin auth
    
    def get_admin_headers(self):
        """Get headers with admin authentication."""
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.admin_token}"
        }
    
    @task(2)
    def get_system_metrics(self):
        """Get system performance metrics."""
        with self.client.get(
            "/admin/metrics", 
            headers=self.get_admin_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                metrics = response.json()
                if "cpu_usage" in metrics and "memory_usage" in metrics:
                    response.success()
                else:
                    response.failure("Missing required metrics")
            else:
                response.failure(f"Get metrics failed: {response.status_code}")
    
    @task(1)
    def cleanup_old_sessions(self):
        """Trigger cleanup of old sessions."""
        cleanup_data = {"days": 7}
        
        with self.client.post(
            "/admin/cleanup/sessions", 
            json=cleanup_data,
            headers=self.get_admin_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                result = response.json()
                if "cleaned_count" in result:
                    response.success()
                else:
                    response.failure("Missing cleanup result")
            else:
                response.failure(f"Cleanup failed: {response.status_code}")
    
    @task(1)
    def get_active_sessions(self):
        """Get all active automation sessions."""
        with self.client.get(
            "/admin/sessions/active", 
            headers=self.get_admin_headers(),
            catch_response=True
        ) as response:
            if response.status_code == 200:
                sessions = response.json()
                if isinstance(sessions, list):
                    response.success()
                else:
                    response.failure("Invalid active sessions response")
            else:
                response.failure(f"Get active sessions failed: {response.status_code}")


# Event handlers for custom metrics
@events.request.add_listener
def on_request(request_type, name, response_time, response_length, exception, context, **kwargs):
    """Custom request handler for additional metrics."""
    if exception:
        print(f"Request failed: {name} - {exception}")


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Handler for test start."""
    print("Load test starting...")
    print(f"Target host: {environment.host}")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Handler for test stop."""
    print("Load test completed.")
    
    # Print summary statistics
    stats = environment.stats
    print(f"Total requests: {stats.total.num_requests}")
    print(f"Failed requests: {stats.total.num_failures}")
    print(f"Average response time: {stats.total.avg_response_time:.2f}ms")
    print(f"Max response time: {stats.total.max_response_time:.2f}ms")


# Custom load test scenarios
class QuickLoadTest(PokemonRPAUser):
    """Quick load test with higher frequency tasks."""
    wait_time = between(0.5, 1.5)
    weight = 3


class StressTestUser(PokemonRPAUser):
    """Stress test user with rapid requests."""
    wait_time = between(0.1, 0.5)
    weight = 1
    
    @task(10)
    def rapid_health_checks(self):
        """Rapid health check requests for stress testing."""
        self.check_system_health()


# Configuration for different test scenarios
# Run with: locust -f locustfile.py --host=http://localhost:8000

# For quick test: locust -f locustfile.py --host=http://localhost:8000 -u 10 -r 2 -t 60s
# For stress test: locust -f locustfile.py --host=http://localhost:8000 -u 50 -r 5 -t 300s
