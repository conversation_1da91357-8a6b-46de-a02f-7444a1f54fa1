"""High-level automation service orchestrating use cases."""

from typing import Optional, List
import asyncio
import structlog
from dependency_injector.wiring import Provide, inject

from ...core.use_cases.adventure_automation import AdventureAutomationUseCase
from ...core.entities.automation import AutomationSession, AutomationStatus
from ...core.interfaces.notifier import INotifier
from ...core.interfaces.repository import IRepository
from .device_service import DeviceService


logger = structlog.get_logger(__name__)


class AutomationService:
    """High-level service for managing automation workflows."""
    
    @inject
    def __init__(
        self,
        adventure_use_case: AdventureAutomationUseCase = Provide["adventure_automation_use_case"],
        device_service: DeviceService = Provide["device_service"],
        notifier: INotifier = Provide["telegram_notifier"],
        repository: IRepository = Provide["database_repository"]
    ):
        self._adventure_use_case = adventure_use_case
        self._device_service = device_service
        self._notifier = notifier
        self._repository = repository
        self._active_sessions: dict[str, AutomationSession] = {}
    
    async def start_adventure_automation(
        self,
        device_id: str,
        user_name: str,
        max_duration_seconds: int = 3600
    ) -> str:
        """Start adventure automation for a user."""
        
        try:
            logger.info(
                "Starting adventure automation",
                device_id=device_id,
                user_name=user_name,
                max_duration=max_duration_seconds
            )
            
            # Verify device is available
            device = await self._device_service.get_device(device_id)
            if not device.is_connected:
                await self._device_service.connect_device(device_id)
            
            # Check if user already has active session
            existing_session = await self._get_active_session_for_user(user_name)
            if existing_session:
                raise ValueError(f"User {user_name} already has an active automation session")
            
            # Execute automation
            session = await self._adventure_use_case.execute_adventure(
                device_id=device_id,
                user_name=user_name,
                max_duration_seconds=max_duration_seconds
            )
            
            # Store session
            self._active_sessions[session.session_id] = session
            await self._repository.save_automation_session(session)
            
            # Send notification
            if session.status == AutomationStatus.COMPLETED:
                await self._notifier.send_success_notification(
                    f"Adventure automation completed for {user_name}",
                    {
                        "session_id": session.session_id,
                        "duration": session.duration_seconds,
                        "actions_count": len(session.actions),
                        "success_rate": session.success_rate
                    }
                )
            else:
                await self._notifier.send_error_notification(
                    f"Adventure automation failed for {user_name}",
                    {"session_id": session.session_id, "error": session.error_message}
                )
            
            logger.info(
                "Adventure automation finished",
                session_id=session.session_id,
                status=session.status.value,
                user_name=user_name
            )
            
            return session.session_id
            
        except Exception as e:
            error_msg = f"Failed to start adventure automation: {str(e)}"
            logger.error(
                "Adventure automation startup failed",
                device_id=device_id,
                user_name=user_name,
                error=str(e)
            )
            
            await self._notifier.send_error_notification(
                f"Failed to start automation for {user_name}",
                {"error": str(e)}
            )
            
            raise RuntimeError(error_msg)
    
    async def stop_automation(self, session_id: str) -> None:
        """Stop an active automation session."""
        
        if session_id not in self._active_sessions:
            raise ValueError(f"Session {session_id} not found or not active")
        
        session = self._active_sessions[session_id]
        session.status = AutomationStatus.CANCELLED
        session.completed_at = asyncio.get_event_loop().time()
        
        # Update in repository
        await self._repository.update_automation_session(session)
        
        # Remove from active sessions
        del self._active_sessions[session_id]
        
        logger.info("Automation session stopped", session_id=session_id)
    
    async def get_session_status(self, session_id: str) -> Optional[AutomationSession]:
        """Get status of an automation session."""
        
        # Check active sessions first
        if session_id in self._active_sessions:
            return self._active_sessions[session_id]
        
        # Check repository
        return await self._repository.get_automation_session(session_id)
    
    async def list_active_sessions(self) -> List[AutomationSession]:
        """List all active automation sessions."""
        return list(self._active_sessions.values())
    
    async def list_user_sessions(
        self,
        user_name: str,
        limit: int = 10
    ) -> List[AutomationSession]:
        """List automation sessions for a specific user."""
        return await self._repository.get_user_sessions(user_name, limit)
    
    async def get_session_statistics(self, session_id: str) -> dict:
        """Get detailed statistics for a session."""
        
        session = await self.get_session_status(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")
        
        # Calculate statistics
        total_actions = len(session.actions)
        successful_actions = sum(1 for action in session.actions if action.is_successful)
        failed_actions = total_actions - successful_actions
        
        action_types = {}
        for action in session.actions:
            action_type = action.action_type.value
            action_types[action_type] = action_types.get(action_type, 0) + 1
        
        avg_action_duration = 0
        if session.actions:
            durations = [a.duration_ms for a in session.actions if a.duration_ms]
            if durations:
                avg_action_duration = sum(durations) / len(durations)
        
        return {
            "session_id": session.session_id,
            "status": session.status.value,
            "user_name": session.user_name,
            "device_id": session.device_id,
            "duration_seconds": session.duration_seconds,
            "total_actions": total_actions,
            "successful_actions": successful_actions,
            "failed_actions": failed_actions,
            "success_rate": session.success_rate,
            "action_types": action_types,
            "average_action_duration_ms": avg_action_duration,
            "started_at": session.started_at.isoformat() if session.started_at else None,
            "completed_at": session.completed_at.isoformat() if session.completed_at else None
        }
    
    async def cleanup_old_sessions(self, days_old: int = 7) -> int:
        """Clean up old automation sessions."""
        
        deleted_count = await self._repository.delete_old_sessions(days_old)
        
        logger.info(
            "Cleaned up old sessions",
            deleted_count=deleted_count,
            days_old=days_old
        )
        
        return deleted_count
    
    async def _get_active_session_for_user(self, user_name: str) -> Optional[AutomationSession]:
        """Get active session for a user."""
        for session in self._active_sessions.values():
            if session.user_name == user_name and session.status == AutomationStatus.RUNNING:
                return session
        return None
    
    async def health_check(self) -> dict:
        """Perform health check of the automation service."""
        
        try:
            # Check device service
            device_health = await self._device_service.health_check()
            
            # Check repository
            repository_health = await self._repository.health_check()
            
            # Check notifier
            notifier_health = await self._notifier.health_check()
            
            # Overall health
            all_healthy = all([
                device_health.get("healthy", False),
                repository_health.get("healthy", False),
                notifier_health.get("healthy", False)
            ])
            
            return {
                "healthy": all_healthy,
                "active_sessions": len(self._active_sessions),
                "components": {
                    "device_service": device_health,
                    "repository": repository_health,
                    "notifier": notifier_health
                }
            }
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            return {
                "healthy": False,
                "error": str(e)
            }
