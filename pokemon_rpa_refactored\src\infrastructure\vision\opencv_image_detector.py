"""OpenCV-based image detection implementation."""

import cv2
import numpy as np
from typing import List, Optional, Tuple
import structlog
from pathlib import Path

from ...core.interfaces.image_detector import IImageDetector
from ...core.entities.automation import DetectionResult, Coordinates


logger = structlog.get_logger(__name__)


class OpenCVImageDetector(IImageDetector):
    """OpenCV implementation of image detection interface."""
    
    def __init__(self, templates_path: str = "assets/templates"):
        self.templates_path = Path(templates_path)
        self._template_cache: dict[str, np.ndarray] = {}
    
    async def detect_template(
        self,
        screenshot: np.ndarray,
        template_name: str,
        threshold: float = 0.4  # Lowered to 0.4 to detect battle.png
    ) -> DetectionResult:
        """Detect a single template in screenshot."""
        
        try:
            template = await self._load_template(template_name)
            if template is None:
                return DetectionResult(
                    found=False,
                    confidence=0.0,
                    template_name=template_name
                )
            
            # Perform template matching
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= threshold:
                # Calculate center coordinates
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                return DetectionResult(
                    found=True,
                    confidence=float(max_val),
                    coordinates=Coordinates(center_x, center_y),
                    template_name=template_name
                )
            else:
                return DetectionResult(
                    found=False,
                    confidence=float(max_val),
                    template_name=template_name
                )
                
        except Exception as e:
            logger.error(
                "Template detection failed",
                template_name=template_name,
                error=str(e)
            )
            return DetectionResult(
                found=False,
                confidence=0.0,
                template_name=template_name
            )
    
    async def detect_multiple_templates(
        self,
        screenshot: np.ndarray,
        template_names: List[str],
        threshold: float = 0.8
    ) -> List[DetectionResult]:
        """Detect multiple templates in screenshot."""
        
        results = []
        for template_name in template_names:
            result = await self.detect_template(screenshot, template_name, threshold)
            results.append(result)
        
        return results
    
    async def find_text_regions(
        self,
        screenshot: np.ndarray,
        min_area: int = 100
    ) -> List[Coordinates]:
        """Find potential text regions in screenshot."""
        
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            
            # Apply threshold to get binary image
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Find contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            text_regions = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area >= min_area:
                    # Get bounding rectangle
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Calculate center
                    center_x = x + w // 2
                    center_y = y + h // 2
                    
                    text_regions.append(Coordinates(center_x, center_y))
            
            logger.debug("Found text regions", count=len(text_regions))
            return text_regions
            
        except Exception as e:
            logger.error("Text region detection failed", error=str(e))
            return []
    
    async def crop_image(
        self,
        image: np.ndarray,
        coordinates: Coordinates,
        width: int,
        height: int
    ) -> np.ndarray:
        """Crop image around specified coordinates."""
        
        try:
            # Calculate crop boundaries
            x1 = max(0, coordinates.x - width // 2)
            y1 = max(0, coordinates.y - height // 2)
            x2 = min(image.shape[1], coordinates.x + width // 2)
            y2 = min(image.shape[0], coordinates.y + height // 2)
            
            # Crop image
            cropped = image[y1:y2, x1:x2]
            
            logger.debug(
                "Image cropped",
                original_size=(image.shape[1], image.shape[0]),
                crop_size=(x2-x1, y2-y1)
            )
            
            return cropped
            
        except Exception as e:
            logger.error("Image cropping failed", error=str(e))
            return image
    
    async def save_image(self, image: np.ndarray, filepath: str) -> bool:
        """Save image to file."""
        
        try:
            success = cv2.imwrite(filepath, image)
            if success:
                logger.debug("Image saved", filepath=filepath)
            else:
                logger.error("Failed to save image", filepath=filepath)
            return success
            
        except Exception as e:
            logger.error("Image save failed", filepath=filepath, error=str(e))
            return False
    
    async def load_image(self, filepath: str) -> Optional[np.ndarray]:
        """Load image from file."""
        
        try:
            image = cv2.imread(filepath)
            if image is not None:
                logger.debug("Image loaded", filepath=filepath)
                return image
            else:
                logger.error("Failed to load image", filepath=filepath)
                return None
                
        except Exception as e:
            logger.error("Image load failed", filepath=filepath, error=str(e))
            return None
    
    async def crop_region(
        self, 
        screenshot: np.ndarray, 
        top_left: Coordinates, 
        bottom_right: Coordinates
    ) -> np.ndarray:
        """Crop a specific region from the screenshot."""
        try:
            x1, y1 = int(top_left.x), int(top_left.y)
            x2, y2 = int(bottom_right.x), int(bottom_right.y)
            
            # Ensure coordinates are within image bounds
            height, width = screenshot.shape[:2]
            x1 = max(0, min(x1, width))
            y1 = max(0, min(y1, height))
            x2 = max(0, min(x2, width))
            y2 = max(0, min(y2, height))
            
            cropped = screenshot[y1:y2, x1:x2]
            logger.debug(f"Cropped region from ({x1},{y1}) to ({x2},{y2})")
            return cropped
            
        except Exception as e:
            logger.error(f"Error cropping region: {e}")
            return screenshot
    
    async def save_screenshot(
        self, 
        screenshot: np.ndarray, 
        file_path: str
    ) -> None:
        """Save screenshot to file."""
        try:
            success = cv2.imwrite(file_path, screenshot)
            if success:
                logger.debug(f"Screenshot saved to {file_path}")
            else:
                logger.error(f"Failed to save screenshot to {file_path}")
        except Exception as e:
            logger.error(f"Error saving screenshot: {e}")
    
    async def load_template(self, template_path: str) -> np.ndarray:
        """Load template image from file."""
        try:
            # Check cache first
            if template_path in self._template_cache:
                return self._template_cache[template_path]
            
            # Load from file
            full_path = self.templates_path / f"{template_path}.png"
            if not full_path.exists():
                # Try without extension
                full_path = self.templates_path / template_path
                if not full_path.exists():
                    logger.error(f"Template not found: {template_path}")
                    return None
            
            template = cv2.imread(str(full_path), cv2.IMREAD_COLOR)
            if template is None:
                logger.error(f"Failed to load template: {template_path}")
                return None
            
            # Cache the template
            self._template_cache[template_path] = template
            logger.debug(f"Loaded template: {template_path}")
            return template
            
        except Exception as e:
            logger.error(f"Error loading template {template_path}: {e}")
            return None
    
    async def preprocess_image(
        self, 
        image: np.ndarray,
        grayscale: bool = True,
        blur: bool = False
    ) -> np.ndarray:
        """Preprocess image for better detection."""
        try:
            processed = image.copy()
            
            if grayscale and len(processed.shape) == 3:
                processed = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)
            
            if blur:
                processed = cv2.GaussianBlur(processed, (5, 5), 0)
            
            logger.debug(f"Preprocessed image: grayscale={grayscale}, blur={blur}")
            return processed
            
        except Exception as e:
            logger.error(f"Error preprocessing image: {e}")
            return image

    async def find_text_regions(self, screenshot: np.ndarray) -> List[Coordinates]:
        """Find regions containing text in the screenshot."""
        try:
            # Convert to grayscale for text detection
            gray = await self.preprocess_image(screenshot, grayscale=True)
            
            # Use MSER (Maximally Stable Extremal Regions) for text detection
            mser = cv2.MSER_create()
            regions, _ = mser.detectRegions(gray)
            
            text_regions = []
            for region in regions:
                # Get bounding box for each region
                x, y, w, h = cv2.boundingRect(region.reshape(-1, 1, 2))
                
                # Filter regions by size (likely to be text)
                if 10 < w < 300 and 10 < h < 100:
                    text_regions.append(Coordinates(x=x, y=y))
            
            logger.debug(f"Found {len(text_regions)} text regions")
            return text_regions
            
        except Exception as e:
            logger.error(f"Error finding text regions: {e}")
            return []

    async def _load_template(self, template_name: str) -> Optional[np.ndarray]:
        """Internal method to load template (for backward compatibility)."""
        return await self.load_template(template_name)
    
    async def health_check(self) -> dict:
        """Perform health check of image detector."""
        
        try:
            # Check if OpenCV is working
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
            
            # Check templates directory
            templates_exist = self.templates_path.exists()
            template_count = len(list(self.templates_path.glob("*.png"))) if templates_exist else 0
            
            return {
                "healthy": True,
                "opencv_version": cv2.__version__,
                "templates_directory_exists": templates_exist,
                "template_count": template_count,
                "cached_templates": len(self._template_cache)
            }
            
        except Exception as e:
            logger.error("Image detector health check failed", error=str(e))
            return {
                "healthy": False,
                "error": str(e)
            }

    def clear_cache(self) -> None:
        """Clear template cache."""
        self._template_cache.clear()
        logger.debug("Template cache cleared")
