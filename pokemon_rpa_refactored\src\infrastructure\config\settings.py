"""Application settings and configuration."""

import os
from typing import Optional
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field


class ADBSettings(BaseSettings):
    """ADB connection settings."""
    host: str = Field(default="localhost", env="ADB_HOST")
    port: int = Field(default=5037, env="ADB_PORT")
    timeout: int = Field(default=30, env="ADB_TIMEOUT")


class DeviceSettings(BaseSettings):
    """Device configuration settings."""
    adb: ADBSettings = Field(default_factory=ADBSettings)
    default_device_id: Optional[str] = Field(default=None, env="DEFAULT_DEVICE_ID")


class ComputerVisionSettings(BaseSettings):
    """Computer vision settings."""
    template_matching_threshold: float = Field(default=0.8, env="CV_TEMPLATE_THRESHOLD")
    template_matching_method: str = Field(default="TM_CCOEFF_NORMED", env="CV_TEMPLATE_METHOD")
    
    tesseract_path: str = Field(
        default="C:/Program Files (x86)/Tesseract-OCR/tesseract.exe",
        env="TESSERACT_PATH"
    )
    ocr_language: str = Field(default="eng", env="OCR_LANGUAGE")
    ocr_config: str = Field(default="--psm 8", env="OCR_CONFIG")


class MongoDBSettings(BaseSettings):
    """MongoDB database settings."""
    host: str = Field(default="localhost", env="MONGODB_HOST")
    port: int = Field(default=27017, env="MONGODB_PORT")
    database: str = Field(default="pokemon_rpa", env="MONGODB_DATABASE")
    username: Optional[str] = Field(default=None, env="MONGODB_USERNAME")
    password: Optional[str] = Field(default=None, env="MONGODB_PASSWORD")
    
    @property
    def connection_string(self) -> str:
        """Build MongoDB connection string."""
        if self.username and self.password:
            return f"mongodb://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
        return f"mongodb://{self.host}:{self.port}/{self.database}"


class TelegramSettings(BaseSettings):
    """Telegram notification settings."""
    enabled: bool = Field(default=False, env="TELEGRAM_ENABLED")
    bot_token: Optional[str] = Field(default=None, env="TELEGRAM_BOT_TOKEN")
    chat_id: Optional[str] = Field(default=None, env="TELEGRAM_CHAT_ID")


class ServicesSettings(BaseSettings):
    """External services settings."""
    telegram: TelegramSettings = Field(default_factory=TelegramSettings)


class AutomationSettings(BaseSettings):
    """Automation behavior settings."""
    click_delay: float = Field(default=0.5, env="AUTOMATION_CLICK_DELAY")
    screenshot_delay: float = Field(default=1.0, env="AUTOMATION_SCREENSHOT_DELAY")
    action_delay: float = Field(default=2.0, env="AUTOMATION_ACTION_DELAY")
    
    max_retries: int = Field(default=3, env="AUTOMATION_MAX_RETRIES")
    backoff_factor: float = Field(default=2.0, env="AUTOMATION_BACKOFF_FACTOR")
    
    adventure_max_duration: int = Field(default=3600, env="ADVENTURE_MAX_DURATION")
    adventure_check_interval: int = Field(default=30, env="ADVENTURE_CHECK_INTERVAL")


class LoggingSettings(BaseSettings):
    """Logging configuration."""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(default="json", env="LOG_FORMAT")  # json or text
    
    file_enabled: bool = Field(default=True, env="LOG_FILE_ENABLED")
    file_path: str = Field(default="./logs/app.log", env="LOG_FILE_PATH")
    file_max_size: str = Field(default="10MB", env="LOG_FILE_MAX_SIZE")
    file_backup_count: int = Field(default=5, env="LOG_FILE_BACKUP_COUNT")
    
    console_enabled: bool = Field(default=True, env="LOG_CONSOLE_ENABLED")
    console_colorize: bool = Field(default=True, env="LOG_CONSOLE_COLORIZE")


class MonitoringSettings(BaseSettings):
    """Monitoring and health check settings."""
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    metrics_port: int = Field(default=8000, env="METRICS_PORT")
    metrics_path: str = Field(default="/metrics", env="METRICS_PATH")
    
    health_check_enabled: bool = Field(default=True, env="HEALTH_CHECK_ENABLED")
    health_check_port: int = Field(default=8001, env="HEALTH_CHECK_PORT")
    health_check_path: str = Field(default="/health", env="HEALTH_CHECK_PATH")


class Settings(BaseSettings):
    """Main application settings."""
    
    # Application info
    app_name: str = Field(default="Pokemon RPA Framework", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    environment: str = Field(default="development", env="APP_ENVIRONMENT")
    debug: bool = Field(default=True, env="APP_DEBUG")
    
    # Component settings
    device: DeviceSettings = Field(default_factory=DeviceSettings)
    computer_vision: ComputerVisionSettings = Field(default_factory=ComputerVisionSettings)
    database: MongoDBSettings = Field(default_factory=MongoDBSettings)
    services: ServicesSettings = Field(default_factory=ServicesSettings)
    automation: AutomationSettings = Field(default_factory=AutomationSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    
    # Paths
    temp_dir: str = Field(default="./temp", env="TEMP_DIR")
    logs_dir: str = Field(default="./logs", env="LOGS_DIR")
    assets_dir: str = Field(default="./assets", env="ASSETS_DIR")
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )

    def __init__(self, **kwargs):
        """Initialize settings and create directories."""
        super().__init__(**kwargs)
        self._create_directories()
    
    def _create_directories(self) -> None:
        """Create necessary directories if they don't exist."""
        directories = [
            self.temp_dir,
            self.logs_dir,
            os.path.dirname(self.logging.file_path)
        ]
        
        for directory in directories:
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get global settings instance (singleton)."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings
