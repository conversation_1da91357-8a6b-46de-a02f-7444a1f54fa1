"""Device management service."""

from typing import List, Optional
import asyncio
import structlog
from dependency_injector.wiring import Provide, inject

from ...core.entities.device import Device, DeviceStatus
from ...core.interfaces.device_controller import IDeviceController
from ...core.interfaces.repository import IRepository


logger = structlog.get_logger(__name__)


class DeviceService:
    """Service for managing Android devices."""
    
    @inject
    def __init__(
        self,
        device_controller: IDeviceController = Provide["adb_device_controller"],
        repository: IRepository = Provide["database_repository"]
    ):
        self._device_controller = device_controller
        self._repository = repository
        self._connected_devices: dict[str, Device] = {}
    
    async def list_available_devices(self) -> List[Device]:
        """List all available devices."""
        try:
            devices = await self._device_controller.list_devices()
            
            # Update device status in repository
            for device in devices:
                await self._repository.save_device(device)
            
            logger.info("Listed available devices", count=len(devices))
            return devices
            
        except Exception as e:
            logger.error("Failed to list devices", error=str(e))
            raise
    
    async def list_devices(self) -> List[Device]:
        """Alias for list_available_devices for consistency."""
        return await self.list_available_devices()
    
    async def connect_device(self, device_id: str) -> Device:
        """Connect to a specific device."""
        try:
            # Check if already connected
            if device_id in self._connected_devices:
                device = self._connected_devices[device_id]
                if device.is_connected:
                    return device
            
            # Connect to device
            await self._device_controller.connect(device_id)
            
            # Get device info
            device_info = await self._device_controller.get_device_info(device_id)
            
            # Create device entity
            device = Device(
                device_id=device_id,
                status=DeviceStatus.CONNECTED,
                info=device_info
            )
            
            # Store in cache and repository
            self._connected_devices[device_id] = device
            await self._repository.save_device(device)
            
            logger.info("Connected to device", device_id=device_id)
            return device
            
        except Exception as e:
            logger.error("Failed to connect to device", device_id=device_id, error=str(e))
            raise
    
    async def disconnect_device(self, device_id: str) -> None:
        """Disconnect from a device."""
        try:
            await self._device_controller.disconnect(device_id)
            
            # Update device status
            if device_id in self._connected_devices:
                device = self._connected_devices[device_id]
                device.status = DeviceStatus.DISCONNECTED
                await self._repository.save_device(device)
                del self._connected_devices[device_id]
            
            logger.info("Disconnected from device", device_id=device_id)
            
        except Exception as e:
            logger.error("Failed to disconnect from device", device_id=device_id, error=str(e))
            raise
    
    async def get_device(self, device_id: str) -> Device:
        """Get device information."""
        # Check cache first
        if device_id in self._connected_devices:
            return self._connected_devices[device_id]
        
        # Check repository
        device = await self._repository.get_device(device_id)
        if device:
            return device
        
        raise ValueError(f"Device {device_id} not found")
    
    async def health_check(self) -> dict:
        """Perform health check of device service."""
        try:
            # Check ADB connection
            devices = await self._device_controller.list_devices()
            
            # Check connected devices
            connected_count = len(self._connected_devices)
            available_count = len(devices)
            
            # Verify each connected device
            healthy_devices = 0
            for device_id, device in self._connected_devices.items():
                try:
                    is_connected = await self._device_controller.is_device_connected(device_id)
                    if is_connected:
                        healthy_devices += 1
                    else:
                        # Update status
                        device.status = DeviceStatus.DISCONNECTED
                        await self._repository.save_device(device)
                except Exception:
                    pass
            
            return {
                "healthy": True,
                "connected_devices": connected_count,
                "available_devices": available_count,
                "healthy_devices": healthy_devices,
                "adb_available": True
            }
            
        except Exception as e:
            logger.error("Device service health check failed", error=str(e))
            return {
                "healthy": False,
                "error": str(e),
                "connected_devices": 0,
                "available_devices": 0,
                "healthy_devices": 0,
                "adb_available": False
            }
