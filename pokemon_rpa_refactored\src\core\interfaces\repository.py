"""Repository interface definition."""

from abc import ABC, abstractmethod
from typing import List, Optional
from datetime import datetime

from ..entities.device import Device
from ..entities.automation import AutomationSession


class IRepository(ABC):
    """Abstract interface for data persistence operations."""
    
    @abstractmethod
    async def save_device(self, device: Device) -> bool:
        """
        Save device information.
        
        Args:
            device: Device entity to save
            
        Returns:
            True if saved successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_device(self, device_id: str) -> Optional[Device]:
        """
        Get device by ID.
        
        Args:
            device_id: Device identifier
            
        Returns:
            Device entity or None if not found
        """
        pass
    
    @abstractmethod
    async def list_devices(self) -> List[Device]:
        """
        List all devices.
        
        Returns:
            List of all device entities
        """
        pass
    
    @abstractmethod
    async def delete_device(self, device_id: str) -> bool:
        """
        Delete device by ID.
        
        Args:
            device_id: Device identifier
            
        Returns:
            True if deleted successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def save_automation_session(self, session: AutomationSession) -> bool:
        """
        Save automation session.
        
        Args:
            session: AutomationSession entity to save
            
        Returns:
            True if saved successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_automation_session(self, session_id: str) -> Optional[AutomationSession]:
        """
        Get automation session by ID.
        
        Args:
            session_id: Session identifier
            
        Returns:
            AutomationSession entity or None if not found
        """
        pass
    
    @abstractmethod
    async def update_automation_session(self, session: AutomationSession) -> bool:
        """
        Update automation session.
        
        Args:
            session: AutomationSession entity to update
            
        Returns:
            True if updated successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def list_automation_sessions(
        self,
        limit: int = 100,
        offset: int = 0
    ) -> List[AutomationSession]:
        """
        List automation sessions with pagination.
        
        Args:
            limit: Maximum number of sessions to return
            offset: Number of sessions to skip
            
        Returns:
            List of automation session entities
        """
        pass
    
    @abstractmethod
    async def get_user_sessions(
        self,
        user_name: str,
        limit: int = 10
    ) -> List[AutomationSession]:
        """
        Get automation sessions for a specific user.
        
        Args:
            user_name: User identifier
            limit: Maximum number of sessions to return
            
        Returns:
            List of user's automation sessions
        """
        pass
    
    @abstractmethod
    async def delete_automation_session(self, session_id: str) -> bool:
        """
        Delete automation session by ID.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if deleted successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def delete_old_sessions(self, days_old: int = 7) -> int:
        """
        Delete automation sessions older than specified days.
        
        Args:
            days_old: Number of days to consider sessions as old
            
        Returns:
            Number of sessions deleted
        """
        pass
    
    @abstractmethod
    async def get_sessions_by_status(
        self,
        status: str,
        limit: int = 100
    ) -> List[AutomationSession]:
        """
        Get automation sessions by status.
        
        Args:
            status: Session status to filter by
            limit: Maximum number of sessions to return
            
        Returns:
            List of sessions with specified status
        """
        pass
    
    @abstractmethod
    async def get_sessions_by_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
        limit: int = 100
    ) -> List[AutomationSession]:
        """
        Get automation sessions within date range.
        
        Args:
            start_date: Start of date range
            end_date: End of date range
            limit: Maximum number of sessions to return
            
        Returns:
            List of sessions within date range
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> dict:
        """
        Perform health check of repository.
        
        Returns:
            Dictionary containing health status and diagnostics
        """
        pass
