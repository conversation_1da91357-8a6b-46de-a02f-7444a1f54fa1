README.md
pyproject.toml
pokemon_rpa_framework.egg-info/PKG-INFO
pokemon_rpa_framework.egg-info/SOURCES.txt
pokemon_rpa_framework.egg-info/dependency_links.txt
pokemon_rpa_framework.egg-info/entry_points.txt
pokemon_rpa_framework.egg-info/requires.txt
pokemon_rpa_framework.egg-info/top_level.txt
src/__init__.py
src/application/__init__.py
src/application/services/automation_service.py
src/application/services/device_service.py
src/core/__init__.py
src/core/entities/automation.py
src/core/entities/device.py
src/core/interfaces/device_controller.py
src/core/interfaces/image_detector.py
src/core/interfaces/notifier.py
src/core/interfaces/ocr_reader.py
src/core/interfaces/repository.py
src/core/use_cases/adventure_automation.py
src/infrastructure/__init__.py
src/infrastructure/adb/adb_device_controller.py
src/infrastructure/config/container.py
src/infrastructure/config/settings.py
src/infrastructure/database/mongo_repository.py
src/infrastructure/external/telegram_notifier.py
src/infrastructure/monitoring/metrics.py
src/infrastructure/ocr/tesseract_ocr_reader.py
src/infrastructure/vision/opencv_image_detector.py
src/presentation/__init__.py
src/presentation/cli/main.py