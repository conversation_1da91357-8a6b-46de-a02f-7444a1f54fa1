"""OCR reader interface definition."""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
import numpy as np

from ..entities.automation import Coordinates


class IOCRReader(ABC):
    """Abstract interface for OCR (Optical Character Recognition) operations."""
    
    @abstractmethod
    async def extract_text(
        self,
        image: np.ndarray,
        language: str = 'eng',
        config: Optional[str] = None
    ) -> List[str]:
        """
        Extract text from image.
        
        Args:
            image: Input image as numpy array
            language: Language code for OCR (e.g., 'eng', 'spa')
            config: OCR configuration string
            
        Returns:
            List of extracted text lines
        """
        pass
    
    @abstractmethod
    async def extract_text_with_confidence(
        self,
        image: np.ndarray,
        language: str = 'eng',
        min_confidence: float = 60.0
    ) -> List[Dict[str, Any]]:
        """
        Extract text with confidence scores and coordinates.
        
        Args:
            image: Input image as numpy array
            language: Language code for OCR
            min_confidence: Minimum confidence threshold (0-100)
            
        Returns:
            List of dictionaries containing text, confidence, and coordinates
        """
        pass
    
    @abstractmethod
    async def find_text_coordinates(
        self,
        image: np.ndarray,
        search_text: str,
        language: str = 'eng'
    ) -> Optional[Coordinates]:
        """
        Find coordinates of specific text in image.
        
        Args:
            image: Input image as numpy array
            search_text: Text to search for
            language: Language code for OCR
            
        Returns:
            Coordinates of found text or None if not found
        """
        pass
    
    @abstractmethod
    async def extract_numbers(
        self,
        image: np.ndarray,
        language: str = 'eng'
    ) -> List[int]:
        """
        Extract numbers from image.
        
        Args:
            image: Input image as numpy array
            language: Language code for OCR
            
        Returns:
            List of extracted numbers
        """
        pass
    
    @abstractmethod
    async def validate_text_presence(
        self,
        image: np.ndarray,
        expected_texts: List[str],
        language: str = 'eng'
    ) -> Dict[str, bool]:
        """
        Validate presence of expected texts in image.
        
        Args:
            image: Input image as numpy array
            expected_texts: List of texts to check for
            language: Language code for OCR
            
        Returns:
            Dictionary mapping each expected text to boolean presence
        """
        pass
    
    @abstractmethod
    async def get_supported_languages(self) -> List[str]:
        """
        Get list of supported languages.
        
        Returns:
            List of supported language codes
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> dict:
        """
        Perform health check of OCR reader.
        
        Returns:
            Dictionary containing health status and diagnostics
        """
        pass
