"""Use case for automating Pokemon GO adventures."""

from typing import Optional, List
import asyncio
import structlog

from ..entities.automation import (
    AutomationSession, 
    AutomationAction, 
    ActionType, 
    GameState,
    DetectionResult
)
from ..entities.device import Device
from ..interfaces.device_controller import <PERSON>ev<PERSON><PERSON><PERSON>roller
from ..interfaces.image_detector import IImageDetector
from ..interfaces.ocr_reader import IOC<PERSON><PERSON>eader


logger = structlog.get_logger(__name__)


class AdventureAutomationUseCase:
    """Use case for automating adventure gameplay."""
    
    def __init__(
        self,
        device_controller: IDeviceController,
        image_detector: IImageDetector,
        ocr_reader: IOCRReader
    ):
        self._device_controller = device_controller
        self._image_detector = image_detector
        self._ocr_reader = ocr_reader
    
    async def execute_adventure(
        self, 
        device_id: str, 
        user_name: str,
        max_duration_seconds: int = 3600
    ) -> AutomationSession:
        """Execute adventure automation."""
        
        session = AutomationSession(
            device_id=device_id,
            user_name=user_name,
            session_type="adventure_automation"
        )
        
        try:
            logger.info(
                "Starting adventure automation",
                device_id=device_id,
                user_name=user_name,
                session_id=session.session_id
            )
            
            session.start()
            
            # Verify device connection
            if not await self._device_controller.is_device_connected(device_id):
                raise RuntimeError(f"Device {device_id} is not connected")
            
            # Main automation loop
            start_time = asyncio.get_event_loop().time()
            while (asyncio.get_event_loop().time() - start_time) < max_duration_seconds:
                
                # Get current game state
                game_state = await self._analyze_game_state(device_id)
                
                # Determine next action based on game state
                action = await self._determine_next_action(game_state)
                
                if action:
                    # Execute the action
                    await self._execute_action(device_id, action)
                    session.add_action(action)
                    
                    logger.debug(
                        "Action executed",
                        action_type=action.action_type.value,
                        session_id=session.session_id
                    )
                
                # Wait before next iteration
                await asyncio.sleep(2.0)
            
            session.complete()
            logger.info(
                "Adventure automation completed",
                session_id=session.session_id,
                duration=session.duration_seconds,
                actions_count=len(session.actions),
                success_rate=session.success_rate
            )
            
        except Exception as e:
            error_msg = f"Adventure automation failed: {str(e)}"
            session.fail(error_msg)
            logger.error(
                "Adventure automation failed",
                error=str(e),
                session_id=session.session_id
            )
        
        return session
    
    async def _analyze_game_state(self, device_id: str) -> GameState:
        """Analyze current game state."""
        
        # Take screenshot
        screenshot = await self._device_controller.take_screenshot(device_id)
        
        # Detect UI elements
        template_paths = [
            "adventure.png",
            "battle.png", 
            "continue.png",
            "ok.png",
            "return.png"
        ]
        
        detection_results = await self._image_detector.detect_multiple_templates(
            screenshot, template_paths
        )
        
        # Read text from screen
        ocr_text = await self._ocr_reader.extract_text(screenshot)
        
        # Determine screen name
        screen_name = self._identify_screen(detection_results)
        
        return GameState(
            screen_name=screen_name,
            detected_elements=detection_results,
            ocr_text=ocr_text
        )
    
    def _identify_screen(self, detection_results: List[DetectionResult]) -> str:
        """Identify current screen based on detected elements."""
        
        # Debug logging for detection results
        logger.debug(f"Detection results: {len(detection_results)} templates checked")
        for result in detection_results:
            logger.debug(f"Template {result.template_name}: found={result.found}, confidence={result.confidence}")
        
        for result in detection_results:
            if result.found and result.template_name:
                if "adventure" in result.template_name:
                    logger.debug("Screen identified as: adventure_selection")
                    return "adventure_selection"
                elif "battle" in result.template_name:
                    logger.debug("Screen identified as: battle")
                    return "battle"
                elif "continue" in result.template_name:
                    logger.debug("Screen identified as: adventure_complete")
                    return "adventure_complete"
        
        logger.debug("Screen identified as: unknown (no templates matched)")
        return "unknown"
    
    async def _determine_next_action(self, game_state: GameState) -> Optional[AutomationAction]:
        """Determine next action based on game state."""
        
        if game_state.screen_name == "adventure_selection":
            # Look for adventure button
            coords = game_state.get_element_coordinates("adventure.png")
            if coords:
                return AutomationAction(
                    action_type=ActionType.CLICK,
                    parameters={"coordinates": coords}
                )
        
        elif game_state.screen_name == "battle":
            # Auto-battle logic
            coords = game_state.get_element_coordinates("battle.png")
            if coords:
                return AutomationAction(
                    action_type=ActionType.CLICK,
                    parameters={"coordinates": coords}
                )
        
        elif game_state.screen_name == "adventure_complete":
            # Continue to next adventure
            coords = game_state.get_element_coordinates("continue.png")
            if coords:
                return AutomationAction(
                    action_type=ActionType.CLICK,
                    parameters={"coordinates": coords}
                )
        
        # Default: take screenshot for analysis
        return AutomationAction(
            action_type=ActionType.SCREENSHOT,
            parameters={}
        )
    
    async def _execute_action(self, device_id: str, action: AutomationAction) -> None:
        """Execute an automation action."""
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            if action.action_type == ActionType.CLICK:
                coords = action.parameters["coordinates"]
                await self._device_controller.click(device_id, coords)
            
            elif action.action_type == ActionType.SWIPE:
                start_coords = action.parameters["start_coordinates"]
                end_coords = action.parameters["end_coordinates"]
                duration = action.parameters.get("duration_ms", 500)
                await self._device_controller.swipe(
                    device_id, start_coords, end_coords, duration
                )
            
            elif action.action_type == ActionType.TYPE_TEXT:
                text = action.parameters["text"]
                await self._device_controller.type_text(device_id, text)
            
            elif action.action_type == ActionType.WAIT:
                duration = action.parameters.get("duration_seconds", 1.0)
                await asyncio.sleep(duration)
            
            elif action.action_type == ActionType.SCREENSHOT:
                screenshot = await self._device_controller.take_screenshot(device_id)
                action.result = {"screenshot_taken": True}
            
            # Calculate execution time
            end_time = asyncio.get_event_loop().time()
            action.duration_ms = int((end_time - start_time) * 1000)
            
        except Exception as e:
            action.error = str(e)
            raise
